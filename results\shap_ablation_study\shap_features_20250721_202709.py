"""
SHAP-Based Optimal Features - Generated 20250721_202709
Best Algorithm: Random Forest (Accuracy: 0.9322)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "productivity_points",  # SHAP: 0.2122
    "strava_title_count",  # SHAP: 0.1224
    "achievement_rate",  # SHAP: 0.0308
    "gamification_balance",  # SHAP: 0.0279
    "title_balance_ratio",  # SHAP: 0.0202
    "total_distance_km",  # SHAP: 0.0181
    "pomokit_title_length",  # SHAP: 0.0165
    "strava_title_length",  # SHAP: 0.0137
    "activity_points",  # SHAP: 0.0124
    "total_title_diversity",  # SHAP: 0.0065
    "strava_unique_words",  # SHAP: 0.0054
    "avg_time_minutes",  # SHAP: 0.0054
    "total_time_minutes",  # SHAP: 0.0054
    "pomokit_title_count",  # SHAP: 0.0050
    "avg_distance_km",  # SHAP: 0.0046
    "pomokit_unique_words",  # SHAP: 0.0009
    "avg_cycles",  # SHAP: 0.0000
    "weekly_efficiency",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "productivity_points",  # SHAP: 0.2122
    "strava_title_count",  # SHAP: 0.1224
    "achievement_rate",  # SHAP: 0.0308
    "gamification_balance",  # SHAP: 0.0279
    "title_balance_ratio",  # SHAP: 0.0202
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "productivity_points",  # SHAP: 0.2122
    "strava_title_count",  # SHAP: 0.1224
    "achievement_rate",  # SHAP: 0.0308
    "gamification_balance",  # SHAP: 0.0279
    "title_balance_ratio",  # SHAP: 0.0202
    "total_distance_km",  # SHAP: 0.0181
    "pomokit_title_length",  # SHAP: 0.0165
    "strava_title_length",  # SHAP: 0.0137
    "activity_points",  # SHAP: 0.0124
    "total_title_diversity",  # SHAP: 0.0065
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "productivity_points",  # SHAP: 0.2122
    "strava_title_count",  # SHAP: 0.1224
    "achievement_rate",  # SHAP: 0.0308
    "gamification_balance",  # SHAP: 0.0279
    "title_balance_ratio",  # SHAP: 0.0202
    "total_distance_km",  # SHAP: 0.0181
    "pomokit_title_length",  # SHAP: 0.0165
    "strava_title_length",  # SHAP: 0.0137
    "activity_points",  # SHAP: 0.0124
    "total_title_diversity",  # SHAP: 0.0065
    "strava_unique_words",  # SHAP: 0.0054
    "avg_time_minutes",  # SHAP: 0.0054
    "total_time_minutes",  # SHAP: 0.0054
    "pomokit_title_count",  # SHAP: 0.0050
    "avg_distance_km",  # SHAP: 0.0046
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "productivity_points": 0.212167,
    "strava_title_count": 0.122376,
    "achievement_rate": 0.030752,
    "gamification_balance": 0.027850,
    "title_balance_ratio": 0.020246,
    "total_distance_km": 0.018126,
    "pomokit_title_length": 0.016457,
    "strava_title_length": 0.013742,
    "activity_points": 0.012396,
    "total_title_diversity": 0.006497,
    "strava_unique_words": 0.005447,
    "avg_time_minutes": 0.005417,
    "total_time_minutes": 0.005375,
    "pomokit_title_count": 0.005048,
    "avg_distance_km": 0.004618,
    "pomokit_unique_words": 0.000887,
    "avg_cycles": 0.000000,
    "weekly_efficiency": 0.000000,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)