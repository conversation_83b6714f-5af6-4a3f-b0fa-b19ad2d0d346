"""
SHAP-Based Optimal Features - Generated 20250721_192029
Best Algorithm: Random Forest (Accuracy: 0.9492)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "consistency_score",  # SHAP: 0.2627
    "total_cycles",  # SHAP: 0.1321
    "gamification_balance",  # SHAP: 0.0172
    "total_distance_km",  # SHAP: 0.0149
    "title_balance_ratio",  # SHAP: 0.0144
    "pomokit_title_length",  # SHAP: 0.0123
    "total_time_minutes",  # SHAP: 0.0120
    "strava_title_length",  # SHAP: 0.0102
    "strava_unique_words",  # SHAP: 0.0097
    "activity_days",  # SHAP: 0.0087
    "avg_time_minutes",  # SHAP: 0.0041
    "avg_distance_km",  # SHAP: 0.0018
    "achievement_rate",  # SHAP: 0.0017
    "pomokit_unique_words",  # SHAP: 0.0006
    "activity_points",  # SHAP: 0.0004
    "productivity_points",  # SHAP: 0.0003
    "pomokit_title_count",  # SHAP: 0.0001
    "total_title_diversity",  # SHAP: 0.0001
    "strava_title_count",  # SHAP: 0.0001
    "work_days",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2627
    "total_cycles",  # SHAP: 0.1321
    "gamification_balance",  # SHAP: 0.0172
    "total_distance_km",  # SHAP: 0.0149
    "title_balance_ratio",  # SHAP: 0.0144
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2627
    "total_cycles",  # SHAP: 0.1321
    "gamification_balance",  # SHAP: 0.0172
    "total_distance_km",  # SHAP: 0.0149
    "title_balance_ratio",  # SHAP: 0.0144
    "pomokit_title_length",  # SHAP: 0.0123
    "total_time_minutes",  # SHAP: 0.0120
    "strava_title_length",  # SHAP: 0.0102
    "strava_unique_words",  # SHAP: 0.0097
    "activity_days",  # SHAP: 0.0087
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2627
    "total_cycles",  # SHAP: 0.1321
    "gamification_balance",  # SHAP: 0.0172
    "total_distance_km",  # SHAP: 0.0149
    "title_balance_ratio",  # SHAP: 0.0144
    "pomokit_title_length",  # SHAP: 0.0123
    "total_time_minutes",  # SHAP: 0.0120
    "strava_title_length",  # SHAP: 0.0102
    "strava_unique_words",  # SHAP: 0.0097
    "activity_days",  # SHAP: 0.0087
    "avg_time_minutes",  # SHAP: 0.0041
    "avg_distance_km",  # SHAP: 0.0018
    "achievement_rate",  # SHAP: 0.0017
    "pomokit_unique_words",  # SHAP: 0.0006
    "activity_points",  # SHAP: 0.0004
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "consistency_score": 0.262731,
    "total_cycles": 0.132145,
    "gamification_balance": 0.017169,
    "total_distance_km": 0.014927,
    "title_balance_ratio": 0.014378,
    "pomokit_title_length": 0.012312,
    "total_time_minutes": 0.012022,
    "strava_title_length": 0.010170,
    "strava_unique_words": 0.009655,
    "activity_days": 0.008666,
    "avg_time_minutes": 0.004062,
    "avg_distance_km": 0.001756,
    "achievement_rate": 0.001713,
    "pomokit_unique_words": 0.000602,
    "activity_points": 0.000358,
    "productivity_points": 0.000323,
    "pomokit_title_count": 0.000109,
    "total_title_diversity": 0.000098,
    "strava_title_count": 0.000052,
    "work_days": 0.000044,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)