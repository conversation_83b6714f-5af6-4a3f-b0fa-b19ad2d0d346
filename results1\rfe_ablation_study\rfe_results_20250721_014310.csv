algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,5,"['total_cycles', 'work_days', 'title_balance_ratio', 'consistency_score', 'pomokit_title_count']",0.5016949152542373,0.070749880902131,0.513300433559363,0.07352977627608752,0.5047824147201145,0.6139080459770115,0.507795619357703,0.006100704103465748,success
logistic_regression,Logistic Regression,10,"['strava_title_length', 'total_cycles', 'gamification_balance', 'work_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'pomokit_title_length']",0.6189362945645821,0.07722464875420029,0.6247124656828157,0.0729636665114429,0.6177495214337319,0.666360153256705,0.7165125055498002,0.09757621098521807,success
logistic_regression,Logistic Regression,15,"['total_title_diversity', 'total_distance_km', 'strava_title_length', 'total_cycles', 'gamification_balance', 'work_days', 'productivity_points', 'avg_distance_km', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'pomokit_title_length']",0.6223845704266511,0.08207762988946464,0.6250402080769483,0.07530915299454966,0.6177316781754346,0.6622860791826308,0.7225025899067634,0.10011801948011234,success
logistic_regression,Logistic Regression,20,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'activity_points', 'strava_unique_words', 'work_days', 'productivity_points', 'avg_distance_km', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'avg_time_minutes', 'total_time_minutes', 'pomokit_title_length']",0.6154880187025131,0.07590325770579145,0.6188249718848833,0.06839030054344496,0.6122820841964288,0.6515453384418902,0.7310936806274974,0.11560566192498423,success
random_forest,Random Forest,5,"['strava_title_length', 'total_cycles', 'work_days', 'productivity_points', 'consistency_score']",0.9347749853886616,0.029464458572343255,0.9193383660465086,0.03832804385684983,0.9191424226908097,0.92301404853129,0.9793806422968773,0.04460565690821572,success
random_forest,Random Forest,10,"['total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'work_days', 'productivity_points', 'activity_days', 'consistency_score', 'pomokit_title_count', 'achievement_rate']",0.9519579193454121,0.03668888160309854,0.9415732406233047,0.04511539107710166,0.9328610895277561,0.9562707535121329,0.9965665236051503,0.04460860425973823,success
random_forest,Random Forest,15,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'work_days', 'productivity_points', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'pomokit_title_length']",0.9416130917592052,0.03543707153205457,0.9240301608931022,0.04629647794158838,0.9233859847838343,0.9289399744572158,1.0,0.058386908240794844,success
random_forest,Random Forest,20,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'activity_points', 'strava_unique_words', 'work_days', 'productivity_points', 'avg_distance_km', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'avg_time_minutes', 'total_time_minutes', 'pomokit_title_length']",0.9416715371127994,0.027730826773699337,0.9252571860851109,0.03301576495192997,0.9241792860183665,0.9296807151979566,1.0,0.058328462887200616,success
gradient_boosting,Gradient Boosting,5,"['total_cycles', 'work_days', 'productivity_points', 'consistency_score', 'pomokit_title_count']",0.9485680888369374,0.0324089893951383,0.9364611719645886,0.039235560452982814,0.9307122124363504,0.9496040868454662,0.9793954417640964,0.03082735292715899,success
gradient_boosting,Gradient Boosting,10,"['total_distance_km', 'strava_title_length', 'total_cycles', 'gamification_balance', 'work_days', 'productivity_points', 'title_balance_ratio', 'consistency_score', 'pomokit_title_count', 'pomokit_title_length']",0.9313851548801871,0.039013926157488825,0.9103403361851161,0.04859985971268948,0.9120902788144167,0.9122733077905492,1.0,0.06861484511981286,success
gradient_boosting,Gradient Boosting,15,"['total_title_diversity', 'total_distance_km', 'strava_title_length', 'total_cycles', 'gamification_balance', 'strava_unique_words', 'work_days', 'productivity_points', 'avg_distance_km', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'total_time_minutes', 'pomokit_title_length']",0.9243717124488603,0.03555063243952122,0.8976141453274649,0.0490862424959071,0.9063679903330442,0.8970114942528735,1.0,0.0756282875511397,success
gradient_boosting,Gradient Boosting,20,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'activity_points', 'strava_unique_words', 'work_days', 'productivity_points', 'avg_distance_km', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'avg_time_minutes', 'total_time_minutes', 'pomokit_title_length']",0.9278199883109293,0.03684236735880257,0.9035707200477484,0.050136027082916756,0.9095656349823017,0.9044189016602809,1.0,0.07218001168907073,success
svm,Support Vector Machine,5,"['total_cycles', 'work_days', 'productivity_points', 'consistency_score', 'pomokit_title_count']",0.5154880187025132,0.026939440954482186,0.4650092244145944,0.05386382864768333,0.37797705218757843,0.669655172413793,0.5257473730945685,0.010259354392055386,success
svm,Support Vector Machine,10,"['total_title_diversity', 'strava_title_length', 'total_cycles', 'work_days', 'productivity_points', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'pomokit_title_length']",0.635885447106955,0.051525139986978115,0.639508540020595,0.045669974552268615,0.6433655384604151,0.7203065134099617,0.6726690839129791,0.036783636806024034,success
svm,Support Vector Machine,15,"['total_title_diversity', 'total_distance_km', 'strava_title_length', 'total_cycles', 'gamification_balance', 'work_days', 'productivity_points', 'avg_distance_km', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'pomokit_title_length']",0.6565166569257744,0.03915166947799978,0.6562403485384323,0.03717574376703878,0.6546178915884385,0.7198084291187741,0.7062009767648364,0.04968431983906196,success
svm,Support Vector Machine,20,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'activity_points', 'strava_unique_words', 'work_days', 'productivity_points', 'avg_distance_km', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'avg_time_minutes', 'total_time_minutes', 'pomokit_title_length']",0.6462887200467563,0.04974060139631366,0.6439882965086691,0.04857232810250288,0.6403438493234114,0.7075095785440614,0.7096381530264909,0.06334943297973461,success
xgboost,XGBoost,5,"['strava_title_count', 'total_cycles', 'work_days', 'productivity_points', 'consistency_score']",0.9554061952074809,0.031804477167276565,0.9463846396376023,0.03796814139311865,0.9372566939233605,0.9636781609195403,0.9604817226579844,0.0050755274505034365,success
xgboost,XGBoost,10,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'work_days', 'productivity_points', 'title_balance_ratio', 'consistency_score']",0.9312682641729981,0.032716558389939254,0.9099464834678705,0.04141438897573536,0.9137611034922862,0.9118263090676884,1.0,0.06873173582700187,success
xgboost,XGBoost,15,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'activity_points', 'work_days', 'productivity_points', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'total_time_minutes', 'pomokit_title_length']",0.9312682641729981,0.028854134269871188,0.9119938668367965,0.037468420747308835,0.9161495394291095,0.9159003831417625,1.0,0.06873173582700187,success
xgboost,XGBoost,20,"['total_title_diversity', 'total_distance_km', 'strava_title_count', 'strava_title_length', 'total_cycles', 'gamification_balance', 'activity_points', 'strava_unique_words', 'work_days', 'productivity_points', 'avg_distance_km', 'activity_days', 'title_balance_ratio', 'pomokit_unique_words', 'consistency_score', 'pomokit_title_count', 'achievement_rate', 'avg_time_minutes', 'total_time_minutes', 'pomokit_title_length']",0.9243717124488603,0.03555063243952122,0.9020598126998438,0.04637036072734101,0.907243013855917,0.9010855683269476,1.0,0.0756282875511397,success
