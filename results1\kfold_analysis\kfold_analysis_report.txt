================================================================================
K-FOLD CROSS-VALIDATION OVERFITTING ANALYSIS REPORT
================================================================================

📊 DATASET INFORMATION:
   • Total samples: 291
   • Features: 20
   • Target distribution: {'medium_risk': np.int64(145), 'high_risk': np.int64(100), 'low_risk': np.int64(46)}
   • K values tested: 2 to 20

🎯 MODEL PERFORMANCE SUMMARY:
   • Logistic Regression:
     - Best validation: 0.6676 ± 0.1417 (k=20)
     - Training at best k: 0.7287
     - Train-val gap: 0.0611
   • Random Forest:
     - Best validation: 0.9588 ± 0.0067 (k=2)
     - Training at best k: 1.0000
     - Train-val gap: 0.0412
   • Gradient Boosting:
     - Best validation: 0.9344 ± 0.0559 (k=15)
     - Training at best k: 1.0000
     - Train-val gap: 0.0656
   • XGBoost:
     - Best validation: 0.9452 ± 0.0256 (k=4)
     - Training at best k: 1.0000
     - Train-val gap: 0.0548

🔍 OVERFITTING ANALYSIS:
   Models ranked by overfitting risk (low to high):
   1. Random Forest - LOW RISK
      • Overfitting score: 4.64
      • Optimal k: 2
      • Max train-val gap: 0.0617
   2. XGBoost - LOW RISK
      • Overfitting score: 5.34
      • Optimal k: 3
      • Max train-val gap: 0.0688
   3. Gradient Boosting - LOW RISK
      • Overfitting score: 6.24
      • Optimal k: 3
      • Max train-val gap: 0.0963
   4. Logistic Regression - MEDIUM RISK
      • Overfitting score: 11.98
      • Optimal k: 2
      • Max train-val gap: 0.1220

💡 RECOMMENDATIONS:
   • BEST MODEL: Random Forest (lowest overfitting risk)
   • RECOMMENDED K: 2
   • AVERAGE OPTIMAL K: 2.5
   • Low k values suggest small dataset - consider data augmentation
================================================================================