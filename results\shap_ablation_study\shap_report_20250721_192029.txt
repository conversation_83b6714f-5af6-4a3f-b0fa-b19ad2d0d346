================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 145, 'high_risk': 100, 'low_risk': 46}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.5932
     - Test F1-Score: 0.5848
     - CV Accuracy: 0.6510 (±0.0333)
     - CV F1-Score: 0.6434 (±0.0371)
   • Random Forest:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9611 (±0.0253)
     - CV F1-Score: 0.9621 (±0.0247)
   • Gradient Boosting:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9395 (±0.0317)
     - CV F1-Score: 0.9392 (±0.0317)
   • XGBoost:
     - Test Accuracy: 0.9322
     - Test F1-Score: 0.9333
     - CV Accuracy: 0.9352 (±0.0276)
     - CV F1-Score: 0.9349 (±0.0273)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. 🔥 consistency_score: 0.2636
      2. 🔥 total_cycles: 0.1297
      3. ▫️ gamification_balance: 0.0192
      4. ▫️ total_distance_km: 0.0167
      5. ▫️ title_balance_ratio: 0.0129
      6. ▫️ pomokit_title_length: 0.0120
      7. ▫️ activity_days: 0.0111
      8. ▫️ strava_title_length: 0.0091
      9. ▫️ total_time_minutes: 0.0085
     10. ▫️ strava_unique_words: 0.0083

   📊 Random Forest:
      1. 🔥 consistency_score: 0.2627
      2. 🔥 total_cycles: 0.1321
      3. ▫️ gamification_balance: 0.0172
      4. ▫️ total_distance_km: 0.0149
      5. ▫️ title_balance_ratio: 0.0144
      6. ▫️ pomokit_title_length: 0.0123
      7. ▫️ total_time_minutes: 0.0120
      8. ▫️ strava_title_length: 0.0102
      9. ▫️ strava_unique_words: 0.0097
     10. ▫️ activity_days: 0.0087

   📊 Gradient Boosting:
      1. 🔥 consistency_score: 0.2628
      2. 🔥 total_cycles: 0.1324
      3. ▫️ gamification_balance: 0.0172
      4. ▫️ title_balance_ratio: 0.0153
      5. ▫️ total_distance_km: 0.0149
      6. ▫️ pomokit_title_length: 0.0126
      7. ▫️ total_time_minutes: 0.0125
      8. ▫️ strava_title_length: 0.0108
      9. ▫️ strava_unique_words: 0.0103
     10. ▫️ activity_days: 0.0085

   📊 XGBoost:
      1. 🔥 consistency_score: 0.2628
      2. 🔥 total_cycles: 0.1322
      3. ▫️ gamification_balance: 0.0174
      4. ▫️ title_balance_ratio: 0.0150
      5. ▫️ total_distance_km: 0.0143
      6. ▫️ pomokit_title_length: 0.0125
      7. ▫️ total_time_minutes: 0.0121
      8. ▫️ strava_title_length: 0.0104
      9. ▫️ strava_unique_words: 0.0097
     10. ▫️ activity_days: 0.0088

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • consistency_score: 4/4 algorithms (100.0%)
     • total_cycles: 4/4 algorithms (100.0%)
     • gamification_balance: 4/4 algorithms (100.0%)
     • total_distance_km: 4/4 algorithms (100.0%)
     • title_balance_ratio: 4/4 algorithms (100.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: Random Forest (0.9492 accuracy)
   • Top 5 SHAP features for production:
     1. consistency_score (SHAP: 0.2627)
     2. total_cycles (SHAP: 0.1321)
     3. gamification_balance (SHAP: 0.0172)
     4. total_distance_km (SHAP: 0.0149)
     5. title_balance_ratio (SHAP: 0.0144)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 20
   • Best accuracy achieved: 0.9492
   • Analysis timestamp: 2025-07-21T19:20:29.056811