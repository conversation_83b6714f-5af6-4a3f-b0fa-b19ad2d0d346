================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 18
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 145, 'high_risk': 100, 'low_risk': 46}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.5763
     - Test F1-Score: 0.5709
     - CV Accuracy: 0.6208 (±0.0548)
     - CV F1-Score: 0.6160 (±0.0554)
   • Random Forest:
     - Test Accuracy: 0.9322
     - Test F1-Score: 0.9349
     - CV Accuracy: 0.9568 (±0.0273)
     - CV F1-Score: 0.9575 (±0.0269)
   • Gradient Boosting:
     - Test Accuracy: 0.8814
     - Test F1-Score: 0.8798
     - CV Accuracy: 0.9308 (±0.0376)
     - CV F1-Score: 0.9266 (±0.0446)
   • XGBoost:
     - Test Accuracy: 0.9322
     - Test F1-Score: 0.9333
     - CV Accuracy: 0.9265 (±0.0353)
     - CV F1-Score: 0.9255 (±0.0360)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. 🔥 productivity_points: 0.2124
      2. 🔥 strava_title_count: 0.1206
      3. 🔸 gamification_balance: 0.0302
      4. 🔸 achievement_rate: 0.0280
      5. 🔸 total_distance_km: 0.0224
      6. 🔸 title_balance_ratio: 0.0223
      7. ▫️ pomokit_title_length: 0.0163
      8. ▫️ activity_points: 0.0136
      9. ▫️ strava_title_length: 0.0126
     10. ▫️ total_title_diversity: 0.0104
     11. ▫️ pomokit_title_count: 0.0082
     12. ▫️ avg_distance_km: 0.0082
     13. ▫️ strava_unique_words: 0.0078
     14. ▫️ avg_time_minutes: 0.0070
     15. ▫️ total_time_minutes: 0.0056
     16. ▫️ pomokit_unique_words: 0.0042
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

   📊 Random Forest:
      1. 🔥 productivity_points: 0.2122
      2. 🔥 strava_title_count: 0.1224
      3. 🔸 achievement_rate: 0.0308
      4. 🔸 gamification_balance: 0.0279
      5. 🔸 title_balance_ratio: 0.0202
      6. ▫️ total_distance_km: 0.0181
      7. ▫️ pomokit_title_length: 0.0165
      8. ▫️ strava_title_length: 0.0137
      9. ▫️ activity_points: 0.0124
     10. ▫️ total_title_diversity: 0.0065
     11. ▫️ strava_unique_words: 0.0054
     12. ▫️ avg_time_minutes: 0.0054
     13. ▫️ total_time_minutes: 0.0054
     14. ▫️ pomokit_title_count: 0.0050
     15. ▫️ avg_distance_km: 0.0046
     16. ▫️ pomokit_unique_words: 0.0009
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

   📊 Gradient Boosting:
      1. 🔥 productivity_points: 0.2116
      2. 🔥 strava_title_count: 0.1220
      3. 🔸 achievement_rate: 0.0309
      4. 🔸 gamification_balance: 0.0279
      5. ▫️ title_balance_ratio: 0.0197
      6. ▫️ total_distance_km: 0.0178
      7. ▫️ pomokit_title_length: 0.0162
      8. ▫️ strava_title_length: 0.0145
      9. ▫️ activity_points: 0.0120
     10. ▫️ strava_unique_words: 0.0060
     11. ▫️ avg_distance_km: 0.0057
     12. ▫️ total_time_minutes: 0.0056
     13. ▫️ total_title_diversity: 0.0055
     14. ▫️ avg_time_minutes: 0.0051
     15. ▫️ pomokit_title_count: 0.0048
     16. ▫️ pomokit_unique_words: 0.0003
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

   📊 XGBoost:
      1. 🔥 productivity_points: 0.2114
      2. 🔥 strava_title_count: 0.1224
      3. 🔸 achievement_rate: 0.0308
      4. 🔸 gamification_balance: 0.0279
      5. ▫️ title_balance_ratio: 0.0198
      6. ▫️ total_distance_km: 0.0179
      7. ▫️ pomokit_title_length: 0.0166
      8. ▫️ strava_title_length: 0.0142
      9. ▫️ activity_points: 0.0126
     10. ▫️ total_title_diversity: 0.0062
     11. ▫️ strava_unique_words: 0.0062
     12. ▫️ total_time_minutes: 0.0062
     13. ▫️ avg_distance_km: 0.0053
     14. ▫️ avg_time_minutes: 0.0051
     15. ▫️ pomokit_title_count: 0.0047
     16. ▫️ pomokit_unique_words: 0.0000
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • productivity_points: 4/4 algorithms (100.0%)
     • strava_title_count: 4/4 algorithms (100.0%)
     • gamification_balance: 4/4 algorithms (100.0%)
     • achievement_rate: 4/4 algorithms (100.0%)
     • title_balance_ratio: 3/4 algorithms (75.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: Random Forest (0.9322 accuracy)
   • Top 5 SHAP features for production:
     1. productivity_points (SHAP: 0.2122)
     2. strava_title_count (SHAP: 0.1224)
     3. achievement_rate (SHAP: 0.0308)
     4. gamification_balance (SHAP: 0.0279)
     5. title_balance_ratio (SHAP: 0.0202)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 18
   • Best accuracy achieved: 0.9322
   • Analysis timestamp: 2025-07-21T20:27:09.353039