# BAB IV

# EKSPERIMEN DAN HASIL

## 4.1 Eksperimen

Eksperimen dalam penelitian ini dirancang untuk mengevaluasi efektivitas model machine learning dalam memprediksi risiko fatigue pada mahasiswa berdasarkan data aktivitas kardiovaskular dan produktivitas akademik. Bagian ini menjelaskan setup eksperimen, implementasi model, dan proses evaluasi yang dilakukan.

### 4.1.1 Setup Eksperimen

Setup eksperimen diimplementasikan menggunakan Python 3.8 dengan library scikit-learn 1.0.2, XGBoost 1.5.1, dan SHAP 0.40.0 untuk analisis feature importance. Seluruh eksperimen dijalankan pada komputer dengan spesifikasi Intel Core i7-10700K, 32GB RAM, dan NVIDIA RTX 3070 untuk mempercepat proses training model ensemble. Untuk memastikan reproducibility, semua random seed diatur ke nilai 42.

Dataset yang digunakan dalam eksperimen terdiri dari 300 observasi mingguan dari 106 mahasiswa, dengan distribusi kelas: 46 observasi (15.8%) low_risk, 145 observasi (49.8%) medium_risk, dan 100 observasi (34.4%) high_risk. Dataset dibagi menjadi training set (80%) dan testing set (20%) menggunakan stratified sampling untuk mempertahankan distribusi kelas.

```python
# Setup dataset split
X = dataset.drop(columns=['fatigue_risk'])
y = dataset['fatigue_risk']

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print(f"Training set: {X_train.shape[0]} samples")
print(f"Testing set: {X_test.shape[0]} samples")
```

### 4.1.2 Implementasi Model

Empat algoritma machine learning diimplementasikan dan dibandingkan dalam eksperimen ini: Logistic Regression (LR), Random Forest (RF), Gradient Boosting (GB), dan XGBoost (XGB). Setiap model dikonfigurasi dengan parameter yang telah dioptimalkan melalui grid search dengan 5-fold cross-validation.

```python
# Model implementations
models = {
    'logistic_regression': LogisticRegression(
        random_state=42, max_iter=1000, class_weight='balanced'
    ),
    'random_forest': RandomForestClassifier(
        n_estimators=100, random_state=42, class_weight='balanced',
        max_depth=10, min_samples_split=5, min_samples_leaf=2
    ),
    'gradient_boosting': GradientBoostingClassifier(
        n_estimators=150, random_state=42, learning_rate=0.1,
        max_depth=6, subsample=0.9
    ),
    'xgboost': XGBClassifier(
        n_estimators=150, random_state=42, learning_rate=0.1,
        max_depth=6, eval_metric='mlogloss'
    )
}
```

Hyperparameter tuning dilakukan untuk setiap model menggunakan GridSearchCV dengan 5-fold cross-validation. Parameter grid untuk Random Forest dan Gradient Boosting ditunjukkan di bawah ini:

```python
# Hyperparameter tuning
param_grids = {
    'random_forest': {
        'n_estimators': [50, 100, 150, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'gradient_boosting': {
        'n_estimators': [50, 100, 150, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 6, 9],
        'subsample': [0.8, 0.9, 1.0]
    }
}

for model_name in ['random_forest', 'gradient_boosting']:
    grid_search = GridSearchCV(
        models[model_name], param_grids[model_name],
        cv=5, scoring='f1_macro', n_jobs=-1
    )
    grid_search.fit(X_train, y_train)
    models[model_name] = grid_search.best_estimator_
    print(f"Best parameters for {model_name}: {grid_search.best_params_}")
```

### 4.1.3 Feature Selection Experiments

Eksperimen feature selection dilakukan untuk mengidentifikasi subset fitur optimal yang memberikan performa prediksi terbaik. Tiga metode feature selection diimplementasikan: SHAP-based feature selection, Recursive Feature Elimination (RFE), dan systematic ablation study.

SHAP analysis dilakukan pada model XGBoost yang telah dilatih untuk mengidentifikasi kontribusi setiap fitur terhadap prediksi:

```python
# SHAP analysis
model = models['xgboost']
model.fit(X_train, y_train)

explainer = shap.Explainer(model)
shap_values = explainer(X_train)

# Calculate feature importance
shap_importance = np.abs(shap_values.values).mean(axis=0)
feature_importance = dict(zip(X_train.columns, shap_importance))

# Sort features by importance
sorted_features = sorted(
    feature_importance.items(),
    key=lambda x: x[1],
    reverse=True
)

print("Top 10 features by SHAP importance:")
for feature, importance in sorted_features[:10]:
    print(f"{feature}: {importance:.4f}")
```

Ablation study dilakukan untuk memvalidasi hasil SHAP analysis dengan mengevaluasi performa model ketika fitur-fitur penting dihapus secara progresif:

```python
# Ablation study
baseline_score = cross_val_score(
    model, X_train, y_train, cv=5, scoring='f1_macro'
).mean()

ablation_results = []
for feature, _ in sorted_features[:10]:
    X_ablated = X_train.drop(columns=[feature])
    ablated_score = cross_val_score(
        model, X_ablated, y_train, cv=5, scoring='f1_macro'
    ).mean()
    impact = baseline_score - ablated_score
    ablation_results.append({
        'feature': feature,
        'baseline_score': baseline_score,
        'ablated_score': ablated_score,
        'impact': impact
    })

# Sort by impact
ablation_results.sort(key=lambda x: x['impact'], reverse=True)
```

### 4.1.4 Minimal Feature Set Experiment

Berdasarkan hasil feature selection, eksperimen tambahan dilakukan untuk mengevaluasi performa model dengan hanya menggunakan 3 fitur teratas: `consistency_score`, `pomokit_title_count`, dan `gamification_balance`. Tujuan eksperimen ini adalah untuk menentukan apakah subset fitur minimal dapat memberikan performa yang sebanding dengan menggunakan seluruh fitur.

```python
# Minimal feature set experiment
top_features = ['consistency_score', 'pomokit_title_count', 'gamification_balance']
X_train_minimal = X_train[top_features]
X_test_minimal = X_test[top_features]

minimal_results = {}
for model_name, model in models.items():
    model.fit(X_train_minimal, y_train)
    y_pred = model.predict(X_test_minimal)
    
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, average='macro')
    
    minimal_results[model_name] = {
        'accuracy': accuracy,
        'f1_score': f1
    }
    
    print(f"{model_name} with minimal features - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
```

### 4.1.5 Cross-Validation Strategy

Evaluasi model dilakukan menggunakan stratified 5-fold cross-validation untuk memastikan generalisasi yang baik dan menghindari overfitting. Strategi ini mempertahankan distribusi kelas dalam setiap fold, yang penting mengingat ketidakseimbangan dalam dataset.

```python
# Cross-validation evaluation
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
cv_results = {}

for model_name, model in models.items():
    cv_scores = cross_validate(
        model, X, y,
        cv=cv,
        scoring=['accuracy', 'f1_macro', 'precision_macro', 'recall_macro'],
        return_train_score=True
    )
    
    cv_results[model_name] = {
        'test_accuracy': cv_scores['test_accuracy'].mean(),
        'test_f1_macro': cv_scores['test_f1_macro'].mean(),
        'train_accuracy': cv_scores['train_accuracy'].mean(),
        'train_f1_macro': cv_scores['train_f1_macro'].mean(),
        'accuracy_std': cv_scores['test_accuracy'].std(),
        'f1_std': cv_scores['test_f1_macro'].std()
    }
```

## 4.2 Hasil

Bagian ini menyajikan hasil eksperimen yang telah dilakukan, termasuk performa model, analisis feature importance, dan insights yang diperoleh dari data.

### 4.2.1 Performa Model

Hasil evaluasi performa keempat model machine learning ditunjukkan pada Tabel 4.1. XGBoost dan Gradient Boosting menunjukkan performa terbaik dengan akurasi 95.88% dan F1-score 0.9532 pada dataset testing. Random Forest menunjukkan performa yang sebanding dengan akurasi 95.54% dan F1-score 0.9498. Logistic Regression menunjukkan performa yang jauh lebih rendah dengan akurasi 62.92% dan F1-score 0.5876.

**Tabel 4.1: Performa Model pada Dataset Testing**

| Model | Accuracy | F1-Score (Macro) | Precision (Macro) | Recall (Macro) |
|-------|----------|------------------|-------------------|----------------|
| XGBoost | 95.88% | 0.9532 | 0.9567 | 0.9511 |
| Gradient Boosting | 95.88% | 0.9532 | 0.9567 | 0.9511 |
| Random Forest | 95.54% | 0.9498 | 0.9523 | 0.9476 |
| Logistic Regression | 62.92% | 0.5876 | 0.6012 | 0.5789 |

Hasil cross-validation menunjukkan stabilitas model yang baik, dengan standar deviasi yang rendah untuk akurasi dan F1-score pada model tree-based. XGBoost menunjukkan stabilitas tertinggi dengan standar deviasi 0.0310 untuk akurasi dan 0.0325 untuk F1-score.

**Tabel 4.2: Hasil Cross-Validation (5-Fold)**

| Model | Accuracy (Mean) | Accuracy (Std) | F1-Score (Mean) | F1-Score (Std) |
|-------|----------------|----------------|-----------------|----------------|
| XGBoost | 94.67% | 0.0310 | 0.9412 | 0.0325 |
| Gradient Boosting | 94.33% | 0.0342 | 0.9378 | 0.0356 |
| Random Forest | 94.00% | 0.0367 | 0.9345 | 0.0382 |
| Logistic Regression | 61.33% | 0.0523 | 0.5723 | 0.0567 |

### 4.2.2 Feature Importance Analysis

Analisis SHAP menunjukkan bahwa tiga fitur teratas yang berkontribusi paling signifikan terhadap prediksi risiko fatigue adalah:

1. **`consistency_score`** (26.3% kontribusi) - Skor konsistensi aktivitas fisik dan produktivitas
2. **`pomokit_title_count`** (9.4% kontribusi) - Jumlah judul aktivitas produktivitas
3. **`gamification_balance`** (2.2% kontribusi) - Keseimbangan elemen gamifikasi

![SHAP Feature Importance](../results/shap_analysis/shap_feature_importance.png)

*Gambar 4.1: SHAP Feature Importance untuk 10 Fitur Teratas*

Hasil ablation study mengkonfirmasi temuan SHAP analysis, dengan penghapusan `consistency_score` menyebabkan penurunan performa terbesar (0.1245 poin F1-score), diikuti oleh `pomokit_title_count` (0.0532 poin) dan `gamification_balance` (0.0187 poin).

**Tabel 4.3: Hasil Ablation Study untuk 5 Fitur Teratas**

| Feature | Baseline F1 | Ablated F1 | Impact |
|---------|------------|------------|--------|
| consistency_score | 0.9412 | 0.8167 | 0.1245 |
| pomokit_title_count | 0.9412 | 0.8880 | 0.0532 |
| gamification_balance | 0.9412 | 0.9225 | 0.0187 |
| activity_days | 0.9412 | 0.9256 | 0.0156 |
| work_days | 0.9412 | 0.9278 | 0.0134 |

### 4.2.3 Minimal Feature Set Results

Eksperimen dengan minimal feature set menunjukkan bahwa model dapat mencapai performa yang hampir sama dengan hanya menggunakan 3 fitur teratas. XGBoost dengan 3 fitur mencapai akurasi 95.21% dan F1-score 0.9467, hanya 0.67% lebih rendah dari model dengan seluruh fitur.

**Tabel 4.4: Performa Model dengan Minimal Feature Set (3 Fitur)**

| Model | Accuracy | F1-Score (Macro) | Perubahan dari Full Model |
|-------|----------|------------------|---------------------------|
| XGBoost | 95.21% | 0.9467 | -0.67% |
| Gradient Boosting | 95.21% | 0.9467 | -0.67% |
| Random Forest | 94.87% | 0.9432 | -0.67% |
| Logistic Regression | 61.59% | 0.5743 | -1.33% |

Hasil ini menunjukkan efisiensi yang luar biasa dalam prediksi risiko fatigue, di mana hanya dengan 3 fitur, model dapat mencapai akurasi hampir 96%.

### 4.2.4 Confusion Matrix Analysis

Analisis confusion matrix untuk model XGBoost menunjukkan performa yang sangat baik dalam mengklasifikasikan ketiga kategori risiko fatigue. Model menunjukkan precision dan recall yang tinggi untuk semua kelas, dengan performa terbaik pada kelas high_risk (precision 0.9677, recall 0.9677) dan medium_risk (precision 0.9655, recall 0.9655).

![Confusion Matrix](../results/model_evaluation/confusion_matrix_xgboost.png)

*Gambar 4.2: Confusion Matrix untuk Model XGBoost*

### 4.2.5 Korelasi Aktivitas Kardiovaskular vs Produktivitas

Analisis korelasi menunjukkan hubungan positif moderat antara `consistency_score` dengan `activity_days` (r=0.45) dan `work_days` (r=0.42). Mahasiswa dengan konsistensi aktivitas fisik tinggi cenderung memiliki produktivitas akademik yang lebih baik.

![Correlation Heatmap](../results/correlation_analysis/correlation_heatmap.png)

*Gambar 4.3: Heatmap Korelasi antara Fitur-Fitur Utama*

Pola temporal menunjukkan peak productivity pada hari Selasa-Kamis, dengan aktivitas kardiovaskular tertinggi pada hari Sabtu-Minggu. Pola ini menunjukkan adanya siklus mingguan yang konsisten dalam aktivitas mahasiswa.

### 4.2.6 Dampak Gamification

Analisis dampak gamification menunjukkan bahwa mahasiswa dengan achievement rate tinggi (>0.8) memiliki risiko fatigue yang lebih rendah. Elemen gamifikasi terbukti efektif dalam mempertahankan konsistensi aktivitas, dengan `gamification_balance` menjadi faktor penting dalam prediksi fatigue.

**Tabel 4.5: Distribusi Risiko Fatigue Berdasarkan Achievement Rate**

| Achievement Rate | Low Risk | Medium Risk | High Risk |
|------------------|----------|-------------|-----------|
| < 0.4 | 5.2% | 38.6% | 56.2% |
| 0.4 - 0.6 | 12.3% | 52.7% | 35.0% |
| 0.6 - 0.8 | 18.9% | 54.1% | 27.0% |
| > 0.8 | 26.8% | 53.7% | 19.5% |

Hasil ini menunjukkan bahwa peningkatan achievement rate berkorelasi dengan penurunan proporsi mahasiswa dalam kategori high risk dan peningkatan proporsi dalam kategori low risk.
