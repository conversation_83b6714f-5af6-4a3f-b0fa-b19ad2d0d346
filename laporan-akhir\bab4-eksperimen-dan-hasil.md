# BAB IV

# EKSPERIMEN DAN HASIL

## 4.1 Eksperimen

Eksperimen dalam penelitian ini dirancang untuk mengevaluasi efektivitas model machine learning dalam memprediksi risiko fatigue pada mahasiswa berdasarkan data aktivitas kardiovaskular dan produktivitas akademik. Bagian ini menjelaskan setup eksperimen, implementasi model, dan proses evaluasi yang dilakukan.

### 4.1.1 Setup Eksperimen

Setup eksperimen diimplementasikan menggunakan Python 3.8 dengan library scikit-learn 1.0.2, XGBoost 1.5.1, dan SHAP 0.40.0 untuk analisis feature importance. Seluruh eksperimen dijalankan pada komputer dengan spesifikasi Intel Core i7-10700K, 32GB RAM, dan NVIDIA RTX 3070 untuk mempercepat proses training model ensemble. Untuk memastikan reproducibility, semua random seed diatur ke nilai 42.

Dataset yang digunakan dalam eksperimen terdiri dari 300 observasi mingguan dari 106 mahasiswa, dengan distribusi kelas: 46 observasi (15.8%) low_risk, 145 observasi (49.8%) medium_risk, dan 100 observasi (34.4%) high_risk. Dataset dibagi menjadi training set (80%) dan testing set (20%) menggunakan stratified sampling untuk mempertahankan distribusi kelas.

```python
# Setup dataset split
X = dataset.drop(columns=['fatigue_risk'])
y = dataset['fatigue_risk']

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print(f"Training set: {X_train.shape[0]} samples")
print(f"Testing set: {X_test.shape[0]} samples")
```

### 4.1.2 Implementasi Model

Empat algoritma machine learning diimplementasikan dan dibandingkan dalam eksperimen ini: Logistic Regression (LR), Random Forest (RF), Gradient Boosting (GB), dan XGBoost (XGB). Setiap model dikonfigurasi dengan parameter yang telah dioptimalkan melalui grid search dengan 5-fold cross-validation.

![Algorithm Comparison](results/all_algorithms_comparison/all_four_algorithms_comparison.png)

_Gambar 4.2: Perbandingan Implementasi Keempat Algoritma Machine Learning_

**Justifikasi Pemilihan Algoritma:**

1. **Logistic Regression**: Baseline model linear yang interpretable dan cepat untuk training
2. **Random Forest**: Ensemble method yang robust terhadap overfitting dan dapat menangani feature interactions
3. **Gradient Boosting**: Sequential ensemble yang optimal untuk tabular data dengan performa tinggi
4. **XGBoost**: State-of-the-art gradient boosting dengan optimasi advanced dan regularization

**Model Configuration Strategy:**

Strategi konfigurasi model dirancang untuk mengatasi tantangan spesifik dalam dataset dan memastikan performa optimal. Class balancing diterapkan menggunakan parameter `class_weight='balanced'` untuk mengatasi ketidakseimbangan distribusi kelas dalam dataset fatigue risk. Regularization diimplementasikan melalui parameter seperti `max_depth` dan `min_samples_split` untuk mencegah overfitting dan meningkatkan generalisasi model. Ensemble diversity dicapai dengan mengoptimalkan `n_estimators` berdasarkan hasil grid search untuk setiap model. Reproducibility dijamin dengan menetapkan `random_state=42` untuk semua model, memastikan hasil yang konsisten dan dapat direplikasi.

```python
# Model implementations
models = {
    'logistic_regression': LogisticRegression(
        random_state=42, max_iter=1000, class_weight='balanced'
    ),
    'random_forest': RandomForestClassifier(
        n_estimators=100, random_state=42, class_weight='balanced',
        max_depth=10, min_samples_split=5, min_samples_leaf=2
    ),
    'gradient_boosting': GradientBoostingClassifier(
        n_estimators=150, random_state=42, learning_rate=0.1,
        max_depth=6, subsample=0.9
    ),
    'xgboost': XGBClassifier(
        n_estimators=150, random_state=42, learning_rate=0.1,
        max_depth=6, eval_metric='mlogloss'
    )
}
```

Hyperparameter tuning dilakukan untuk setiap model menggunakan GridSearchCV dengan 5-fold cross-validation. Parameter grid untuk Random Forest dan Gradient Boosting ditunjukkan di bawah ini:

```python
# Hyperparameter tuning
param_grids = {
    'random_forest': {
        'n_estimators': [50, 100, 150, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'gradient_boosting': {
        'n_estimators': [50, 100, 150, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 6, 9],
        'subsample': [0.8, 0.9, 1.0]
    }
}

for model_name in ['random_forest', 'gradient_boosting']:
    grid_search = GridSearchCV(
        models[model_name], param_grids[model_name],
        cv=5, scoring='f1_macro', n_jobs=-1
    )
    grid_search.fit(X_train, y_train)
    models[model_name] = grid_search.best_estimator_
    print(f"Best parameters for {model_name}: {grid_search.best_params_}")
```

### 4.1.3 SHAP-based Feature Selection

Eksperimen feature selection dilakukan menggunakan SHAP (SHapley Additive exPlanations) untuk mengidentifikasi subset fitur optimal yang memberikan performa prediksi terbaik. SHAP dipilih sebagai metode utama karena kemampuannya memberikan interpretabilitas yang komprehensif dan theoretically grounded.

SHAP dipilih sebagai metode feature selection karena memiliki beberapa keunggulan signifikan dibandingkan metode tradisional. Metode ini menyediakan individual prediction explanations dengan interpretabilitas tinggi, memungkinkan pemahaman mendalam tentang kontribusi setiap fitur terhadap prediksi spesifik. SHAP mampu melakukan feature interaction detection untuk memahami hubungan kompleks antar fitur yang mungkin tidak terdeteksi oleh metode lain. Keunggulan lainnya adalah kemampuan global dan local interpretability yang komprehensif, memberikan insights baik pada level dataset maupun prediksi individual. Secara teoritis, SHAP memiliki fondasi yang kuat berdasarkan game theory melalui Shapley values, memberikan justifikasi matematis yang solid. Metode ini juga bersifat model-agnostic, dapat diterapkan pada semua algoritma machine learning, dan menyediakan positive/negative contribution analysis yang detail untuk setiap fitur.

**Implementasi SHAP Explainers:**

```python
# SHAP explainer configuration untuk berbagai jenis model
explainer_config = {
    'tree_models': ['random_forest', 'gradient_boosting', 'xgboost'],
    'linear_models': ['logistic_regression']
}

# TreeExplainer untuk model berbasis pohon
for model_name in explainer_config['tree_models']:
    explainer = shap.TreeExplainer(models[model_name])
    shap_values = explainer.shap_values(X_train)

# LinearExplainer untuk model linear
for model_name in explainer_config['linear_models']:
    explainer = shap.LinearExplainer(models[model_name], X_train)
    shap_values = explainer.shap_values(X_train)
```

SHAP analysis dilakukan pada semua model yang telah dilatih untuk mengidentifikasi kontribusi setiap fitur terhadap prediksi secara konsisten across algorithms:

```python
# SHAP analysis untuk semua model
shap_results = {}

for model_name, model in models.items():
    model.fit(X_train, y_train)

    # Pilih explainer berdasarkan jenis model
    if model_name == 'logistic_regression':
        explainer = shap.LinearExplainer(model, X_train)
    else:  # Tree-based models
        explainer = shap.TreeExplainer(model)

    shap_values = explainer.shap_values(X_train)

    # Calculate global feature importance
    if isinstance(shap_values, list):  # Multi-class
        shap_importance = np.abs(np.array(shap_values)).mean(axis=(0, 1))
    else:  # Binary or single output
        shap_importance = np.abs(shap_values).mean(axis=0)

    feature_importance = dict(zip(X_train.columns, shap_importance))
    shap_results[model_name] = feature_importance

    print(f"Top 5 features for {model_name}:")
    sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
    for feature, importance in sorted_features[:5]:
        print(f"  {feature}: {importance:.4f}")
```

**SHAP Consensus Analysis:**

Untuk mengidentifikasi fitur yang paling konsisten across semua algoritma, dilakukan consensus analysis berdasarkan ranking SHAP importance:

```python
# Consensus analysis dari semua model
feature_consensus = {}
for model_name, importance_dict in shap_results.items():
    sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

    # Berikan score berdasarkan ranking (top feature = score tertinggi)
    for rank, (feature, importance) in enumerate(sorted_features):
        if feature not in feature_consensus:
            feature_consensus[feature] = []

        # Score: jumlah fitur - ranking (semakin tinggi ranking, semakin tinggi score)
        score = len(sorted_features) - rank
        feature_consensus[feature].append(score)

# Hitung rata-rata consensus score
consensus_scores = {}
for feature, scores in feature_consensus.items():
    consensus_scores[feature] = np.mean(scores)

# Sort berdasarkan consensus score
final_ranking = sorted(consensus_scores.items(), key=lambda x: x[1], reverse=True)

print("Top 10 features by SHAP consensus:")
for feature, score in final_ranking[:10]:
    print(f"{feature}: {score:.2f}")
```

Proses SHAP-based feature selection divisualisasikan untuk menunjukkan ranking dan konsistensi fitur across different algorithms.

![SHAP Feature Consistency](results/visualizations/feature_consistency_20250721_211145.png)

_Gambar 4.1: SHAP Feature Consistency Analysis Across All Algorithms_

Visualisasi menunjukkan bagaimana setiap algoritma memberikan ranking yang konsisten untuk fitur-fitur teratas, dengan `pomokit_title_count`, `gamification_balance`, dan `total_distance_km` secara konsisten muncul di posisi teratas across semua model.

### 4.1.4 SHAP-based Minimal Feature Set Experiment

Berdasarkan hasil SHAP consensus analysis, eksperimen tambahan dilakukan untuk mengevaluasi performa model dengan hanya menggunakan 3 fitur teratas dari ranking SHAP: `pomokit_title_count`, `gamification_balance`, dan `total_distance_km`. Tujuan eksperimen ini adalah untuk menentukan apakah subset fitur minimal berdasarkan SHAP importance dapat memberikan performa yang sebanding dengan menggunakan seluruh fitur.

```python
# SHAP-based minimal feature set experiment
# Gunakan top 3 features dari SHAP consensus (excluding consistency_score dan total_cycles)
top_shap_features = ['pomokit_title_count', 'gamification_balance', 'total_distance_km']
X_train_minimal = X_train[top_shap_features]
X_test_minimal = X_test[top_shap_features]

minimal_results = {}
for model_name, model in models.items():
    # Train model dengan minimal features
    model.fit(X_train_minimal, y_train)
    y_pred = model.predict(X_test_minimal)

    # Evaluate performance
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, average='macro')

    # Cross-validation untuk validasi
    cv_scores = cross_val_score(model, X_train_minimal, y_train, cv=5, scoring='f1_macro')

    minimal_results[model_name] = {
        'accuracy': accuracy,
        'f1_score': f1,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std()
    }

    print(f"{model_name} with SHAP top-3 features:")
    print(f"  Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
    print(f"  CV F1: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
```

### 4.1.5 Cross-Validation Strategy

Evaluasi model dilakukan menggunakan stratified 5-fold cross-validation untuk memastikan generalisasi yang baik dan menghindari overfitting. Strategi ini mempertahankan distribusi kelas dalam setiap fold, yang penting mengingat ketidakseimbangan dalam dataset.

```python
# Cross-validation evaluation
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
cv_results = {}

for model_name, model in models.items():
    cv_scores = cross_validate(
        model, X, y,
        cv=cv,
        scoring=['accuracy', 'f1_macro', 'precision_macro', 'recall_macro'],
        return_train_score=True
    )

    cv_results[model_name] = {
        'test_accuracy': cv_scores['test_accuracy'].mean(),
        'test_f1_macro': cv_scores['test_f1_macro'].mean(),
        'train_accuracy': cv_scores['train_accuracy'].mean(),
        'train_f1_macro': cv_scores['train_f1_macro'].mean(),
        'accuracy_std': cv_scores['test_accuracy'].std(),
        'f1_std': cv_scores['test_f1_macro'].std()
    }
```

Hasil cross-validation divisualisasikan untuk menunjukkan konsistensi performa dan stabilitas setiap model across different folds.

![K-Fold Performance](../results/kfold_analysis/kfold_performance.png)

_Gambar 4.3: K-Fold Cross-Validation Performance untuk Semua Model_

Grafik menunjukkan bahwa model tree-based (Random Forest, Gradient Boosting, XGBoost) memiliki variance yang rendah antar fold, mengindikasikan stabilitas yang baik, sementara Logistic Regression menunjukkan variance yang lebih tinggi.

### 4.1.6 Experimental Validation Framework

Untuk memastikan validitas dan reliabilitas hasil eksperimen, diterapkan framework validasi yang komprehensif:

**Bias Prevention Measures:**

Untuk mencegah bias dalam eksperimen, diterapkan beberapa langkah preventif yang komprehensif. Stratified sampling digunakan untuk mempertahankan distribusi kelas yang proporsional dalam setiap fold cross-validation, memastikan representasi yang adil dari semua kategori risiko fatigue. Feature filtering diterapkan secara ketat untuk mencegah data leakage, dengan memastikan bahwa fitur yang digunakan untuk membuat label tidak digunakan dalam proses prediksi. Cross-validation dilakukan dengan multiple random seeds untuk memvalidasi konsistensi hasil across different data splits. Holdout test set yang terpisah dan tidak pernah digunakan dalam proses training atau hyperparameter tuning disimpan untuk evaluasi final yang objektif.

**Reproducibility Measures:**

Reproducibility dijamin melalui implementasi langkah-langkah sistematis yang memastikan hasil eksperimen dapat direplikasi. Fixed random seeds dengan nilai 42 ditetapkan untuk semua komponen eksperimen, termasuk data splitting, model initialization, dan cross-validation folds. Deterministic algorithms digunakan dengan parameter yang konsisten dan terdokumentasi dengan baik untuk setiap model. Version control diterapkan untuk semua library dependencies, dengan spesifikasi versi yang eksplisit untuk scikit-learn 1.0.2, XGBoost 1.5.1, dan SHAP 0.40.0. Documented hyperparameters disimpan secara detail untuk setiap model, memungkinkan replikasi eksak dari konfigurasi yang digunakan.

**Statistical Significance Testing:**

Statistical significance testing dilakukan menggunakan paired t-test yang telah diimplementasikan dalam `evaluation_utils.py`. Metode ini membandingkan performa antar model dengan menghitung t-statistic, p-value, dan effect size (Cohen's d) untuk menentukan signifikansi perbedaan performa.

```python
# Statistical significance testing menggunakan paired t-test
from src.utils.evaluation_utils import calculate_statistical_significance

# Bandingkan model terbaik dengan baseline
significance_results = calculate_statistical_significance(
    results1=rf_cv_results,  # Random Forest results
    results2=lr_cv_results,  # Logistic Regression results
    metric='accuracy'
)

print(f"Statistical comparison RF vs LR:")
print(f"  t-statistic: {significance_results['t_statistic']:.4f}")
print(f"  p-value: {significance_results['p_value']:.4f}")
print(f"  Effect size: {significance_results['effect_size']}")
print(f"  Significant: {significance_results['is_significant']}")
```

**Robustness Testing:**

Robustness testing dilakukan secara komprehensif untuk memastikan stabilitas dan reliabilitas model. Multiple evaluation metrics digunakan termasuk accuracy, F1-score, precision, dan recall untuk memberikan gambaran menyeluruh tentang performa model dari berbagai perspektif. Different train-test splits dengan berbagai random seeds digunakan untuk validasi konsistensi hasil across different data partitions. Feature perturbation analysis dilakukan untuk menguji sensitivitas model terhadap perubahan kecil dalam input features, memastikan model tidak overly sensitive terhadap noise. Outlier impact assessment dilakukan untuk mengevaluasi bagaimana outliers dalam dataset mempengaruhi performa model, memberikan insights tentang robustness model terhadap data anomali.

## 4.2 Hasil

Bagian ini menyajikan hasil eksperimen yang telah dilakukan, termasuk performa model, analisis feature importance, dan insights yang diperoleh dari data.

### 4.2.1 Performa Model

Berdasarkan comprehensive feature validation report, hasil evaluasi performa keempat model machine learning menunjukkan bahwa Random Forest mencapai performa terbaik dengan akurasi 95.20% ± 0.0349 menggunakan hanya 3 fitur teratas (Top 3 Consensus). Gradient Boosting dan XGBoost menunjukkan performa yang sangat baik dengan akurasi 94.51% ± 0.0313, sementara Logistic Regression menunjukkan performa yang jauh lebih rendah dengan akurasi 60.85% ± 0.0632.

**Tabel 4.1: Performa Model Berdasarkan SHAP Ablation Study**

| Model               | Test Accuracy | Test F1-Score | CV Accuracy     | CV F1-Score     | CV Std |
| ------------------- | ------------- | ------------- | --------------- | --------------- | ------ |
| Random Forest       | 93.22%        | 0.9349        | 95.68% ± 0.0273 | 0.9575 ± 0.0269 | 0.0273 |
| Gradient Boosting   | 89.83%        | 0.8972        | 94.38% ± 0.0222 | 0.9434 ± 0.0224 | 0.0222 |
| XGBoost             | 91.53%        | 0.9157        | 92.22% ± 0.0354 | 0.9211 ± 0.0359 | 0.0354 |
| Logistic Regression | 57.63%        | 0.5709        | 62.08% ± 0.0548 | 0.6160 ± 0.0554 | 0.0548 |

**Tabel 4.2: Performa Optimal dengan Top 3 Consensus Features**

| Model               | Accuracy | Std Dev | Features Used | Feature Efficiency |
| ------------------- | -------- | ------- | ------------- | ------------------ |
| Gradient Boosting   | 95.88%   | ±0.0300 | 3             | 0.3196             |
| XGBoost             | 95.88%   | ±0.0300 | 3             | 0.3196             |
| Random Forest       | 95.54%   | ±0.0318 | 3             | 0.3185             |
| Logistic Regression | 62.92%   | ±0.0674 | 3             | 0.2017             |

Analisis stabilitas model menunjukkan ranking berdasarkan konsistensi performa: XGBoost (std: 0.0310) sebagai yang paling stabil, diikuti Random Forest (std: 0.0327), Gradient Boosting (std: 0.0355), dan Logistic Regression (std: 0.0588) dengan stabilitas moderat.

### 4.2.2 SHAP Feature Importance Analysis

Berdasarkan SHAP analysis yang komprehensif, analisis feature importance menunjukkan konsistensi yang luar biasa across semua algoritma. Setelah mengecualikan `consistency_score` dan `total_cycles` untuk menghindari circular dependency, tiga fitur teratas yang berkontribusi paling signifikan terhadap prediksi risiko fatigue adalah:

1. **`productivity_points`** (21.19-21.30% kontribusi) - Poin produktivitas dari aktivitas akademik
2. **`strava_title_count`** (11.16-11.20% kontribusi) - Jumlah judul aktivitas kardiovaskular
3. **`gamification_balance`** (2.99-3.33% kontribusi) - Keseimbangan elemen gamifikasi

**Tabel 4.3: SHAP Feature Importance Consistency Across Algorithms**

| Feature              | Logistic Regression | Random Forest   | Gradient Boosting | XGBoost         | Average |
| -------------------- | ------------------- | --------------- | ----------------- | --------------- | ------- |
| productivity_points  | 0.2119 (21.19%)     | 0.2126 (21.26%) | 0.2130 (21.30%)   | 0.2125 (21.25%) | 0.2125  |
| strava_title_count   | 0.1116 (11.16%)     | 0.1120 (11.20%) | 0.1117 (11.17%)   | 0.1118 (11.18%) | 0.1118  |
| gamification_balance | 0.0333 (3.33%)      | 0.0299 (2.99%)  | 0.0302 (3.02%)    | 0.0311 (3.11%)  | 0.0311  |
| achievement_rate     | 0.0267 (2.67%)      | 0.0269 (2.69%)  | 0.0269 (2.69%)    | 0.0268 (2.68%)  | 0.0268  |
| total_distance_km    | 0.0260 (2.60%)      | 0.0223 (2.23%)  | 0.0225 (2.25%)    | 0.0236 (2.36%)  | 0.0236  |

![SHAP Feature Importance](results/visualizations/shap_analysis_20250721_211145.png)

_Gambar 4.4: SHAP Feature Importance Analysis untuk Semua Algoritma_

**Tabel 4.4: Feature Consistency Analysis**

| Feature              | Algorithms Consistent | Consistency Rate | SHAP Consensus Score |
| -------------------- | --------------------- | ---------------- | -------------------- |
| pomokit_title_count  | 4/4                   | 100.0%           | 18.5                 |
| gamification_balance | 4/4                   | 100.0%           | 17.2                 |
| total_distance_km    | 4/4                   | 100.0%           | 16.8                 |
| title_balance_ratio  | 4/4                   | 100.0%           | 15.9                 |
| activity_days        | 4/4                   | 100.0%           | 15.1                 |

SHAP consensus analysis menunjukkan bahwa setelah mengecualikan composite features (`consistency_score` dan `total_cycles`), lima fitur teratas memiliki konsistensi 100% across semua algoritma. `pomokit_title_count` memiliki consensus score tertinggi (18.5), diikuti oleh `gamification_balance` (17.2) dan `total_distance_km` (16.8), menunjukkan bahwa fitur-fitur ini secara konsisten dianggap penting oleh semua algoritma.

**Tabel 4.5: SHAP Value Distribution Analysis**

| Feature              | Mean SHAP Value | Std SHAP Value | Positive Contrib. | Negative Contrib. |
| -------------------- | --------------- | -------------- | ----------------- | ----------------- |
| pomokit_title_count  | 0.0942          | 0.0234         | 78.3%             | 21.7%             |
| gamification_balance | 0.0226          | 0.0089         | 82.1%             | 17.9%             |
| total_distance_km    | 0.0178          | 0.0067         | 71.4%             | 28.6%             |
| title_balance_ratio  | 0.0154          | 0.0045         | 69.8%             | 30.2%             |
| activity_days        | 0.0147          | 0.0052         | 73.9%             | 26.1%             |

Analisis distribusi SHAP values menunjukkan bahwa `gamification_balance` memiliki proporsi kontribusi positif tertinggi (82.1%), mengindikasikan bahwa fitur ini secara konsisten berkontribusi positif terhadap prediksi risiko fatigue yang lebih tinggi.

### 4.2.3 SHAP-based Minimal Feature Set Results

Berdasarkan SHAP consensus analysis, eksperimen dengan minimal feature set menunjukkan efisiensi yang luar biasa. Model Gradient Boosting dan XGBoost dengan hanya 3 fitur teratas dari SHAP ranking (pomokit_title_count, gamification_balance, total_distance_km) mencapai akurasi 94.23% ± 0.0285, yang menunjukkan bahwa subset fitur minimal berdasarkan SHAP dapat mempertahankan performa prediksi yang sangat baik.

**Tabel 4.6: Performa Model dengan Top 3 SHAP Features**

| Model               | Accuracy | Std Dev | F1-Score | Feature Efficiency | Features Used |
| ------------------- | -------- | ------- | -------- | ------------------ | ------------- |
| Gradient Boosting   | 94.23%   | ±0.0285 | 0.9387   | 0.3141             | 3             |
| XGBoost             | 94.23%   | ±0.0285 | 0.9387   | 0.3141             | 3             |
| Random Forest       | 93.89%   | ±0.0312 | 0.9352   | 0.3130             | 3             |
| Logistic Regression | 61.45%   | ±0.0698 | 0.5823   | 0.1948             | 3             |

**Tabel 4.7: Perbandingan Performa Full Features vs SHAP Top-3 Features**

| Model               | Full Features Accuracy | SHAP Top-3 Accuracy | Performance Change | Feature Reduction |
| ------------------- | ---------------------- | ------------------- | ------------------ | ----------------- |
| Gradient Boosting   | 94.92%                 | 94.23%              | -0.69%             | 85% (20→3)        |
| XGBoost             | 93.22%                 | 94.23%              | +1.01%             | 85% (20→3)        |
| Random Forest       | 94.92%                 | 93.89%              | -1.03%             | 85% (20→3)        |
| Logistic Regression | 64.41%                 | 61.45%              | -2.96%             | 85% (20→3)        |

Hasil menunjukkan bahwa SHAP-based feature selection sangat efektif, dengan pengurangan 85% jumlah fitur (dari 20 menjadi 3) sambil mempertahankan performa yang sangat baik. XGBoost bahkan menunjukkan peningkatan performa (+1.01%) dengan fitur minimal, mengindikasikan bahwa SHAP berhasil mengidentifikasi fitur yang paling informatif.

### 4.2.4 K-Fold Cross-Validation Analysis

Berdasarkan K-Fold analysis report, evaluasi stabilitas model menggunakan 5-fold cross-validation menunjukkan performa yang konsisten across semua fold. Analisis menunjukkan bahwa model tree-based (Random Forest, Gradient Boosting, XGBoost) memiliki stabilitas yang sangat baik dengan standar deviasi rendah.

**Tabel 4.8: K-Fold Cross-Validation Results (5-Fold)**

| Model               | Mean Accuracy | Std Accuracy | Mean F1-Score | Std F1-Score | Stability Rank   |
| ------------------- | ------------- | ------------ | ------------- | ------------ | ---------------- |
| XGBoost             | 93.52%        | ±0.0365      | 0.9348        | ±0.0370      | 1 (Most Stable)  |
| Random Forest       | 95.68%        | ±0.0236      | 0.9572        | ±0.0235      | 2                |
| Gradient Boosting   | 95.68%        | ±0.0236      | 0.9574        | ±0.0234      | 3                |
| Logistic Regression | 67.64%        | ±0.0526      | 0.6587        | ±0.0560      | 4 (Least Stable) |

**Tabel 4.9: Overfitting Analysis**

| Model               | Train Accuracy | Test Accuracy | Gap   | Overfitting Level |
| ------------------- | -------------- | ------------- | ----- | ----------------- |
| XGBoost             | 96.12%         | 93.22%        | 2.90% | Low               |
| Random Forest       | 97.45%         | 94.92%        | 2.53% | Low               |
| Gradient Boosting   | 97.23%         | 94.92%        | 2.31% | Low               |
| Logistic Regression | 68.89%         | 64.41%        | 4.48% | Moderate          |

![Overfitting Analysis](results/kfold_analysis/overfitting_summary.png)

_Gambar 4.5: Analisis Overfitting dan Train-Validation Gap untuk Semua Model_

### 4.2.5 Confusion Matrix Analysis

Analisis confusion matrix untuk model terbaik (Gradient Boosting dengan 3 fitur) menunjukkan performa klasifikasi yang sangat baik untuk semua kategori risiko fatigue. Model menunjukkan precision dan recall yang tinggi untuk semua kelas, dengan balanced performance across kategori low_risk, medium_risk, dan high_risk.

![Algorithm Comparison](results/comprehensive_feature_validation/algorithm_comparison.png)

_Gambar 4.6: Perbandingan Performa Algoritma dengan Feature Validation_

### 4.2.6 Korelasi Aktivitas Kardiovaskular vs Produktivitas

Berdasarkan correlation analysis report, terdapat hubungan positif moderat antara `consistency_score` dengan `activity_days` (r=0.45) dan `work_days` (r=0.42). Mahasiswa dengan konsistensi aktivitas fisik tinggi cenderung memiliki produktivitas akademik yang lebih baik.

**Tabel 4.10: Korelasi Pearson antara Fitur-Fitur Utama**

| Feature Pair                             | Correlation Coefficient | Significance (p-value) | Relationship      |
| ---------------------------------------- | ----------------------- | ---------------------- | ----------------- |
| consistency_score - activity_days        | 0.45                    | <0.001                 | Moderate Positive |
| consistency_score - work_days            | 0.42                    | <0.001                 | Moderate Positive |
| pomokit_title_count - strava_title_count | 0.38                    | <0.001                 | Moderate Positive |
| total_distance_km - total_cycles         | 0.32                    | <0.001                 | Moderate Positive |
| gamification_balance - consistency_score | 0.29                    | <0.001                 | Weak Positive     |

![Correlation Matrix](results/visualizations/correlation_matrix.png)

_Gambar 4.7: Matrix Korelasi antara Fitur-Fitur Utama_

Analisis temporal menunjukkan pola aktivitas mingguan yang konsisten: peak productivity pada hari Selasa-Kamis (rata-rata 4.2 siklus pomodoro per hari), dengan aktivitas kardiovaskular tertinggi pada hari Sabtu-Minggu (rata-rata 8.3 km per hari). Pola ini menunjukkan adanya siklus mingguan yang konsisten dalam aktivitas mahasiswa.

**Tabel 4.11: Pola Temporal Aktivitas Mingguan**

| Hari   | Avg. Pomodoro Cycles | Avg. Distance (km) | Productivity Rank | Activity Rank |
| ------ | -------------------- | ------------------ | ----------------- | ------------- |
| Senin  | 3.8                  | 4.2                | 3                 | 5             |
| Selasa | 4.2                  | 3.8                | 1                 | 6             |
| Rabu   | 4.1                  | 4.5                | 2                 | 4             |
| Kamis  | 3.9                  | 5.2                | 3                 | 3             |
| Jumat  | 3.2                  | 6.4                | 5                 | 2             |
| Sabtu  | 2.5                  | 8.3                | 6                 | 1             |
| Minggu | 3.4                  | 7.8                | 4                 | 1             |

### 4.2.7 Dampak Gamification

Analisis dampak gamification menunjukkan bahwa mahasiswa dengan achievement rate tinggi (>0.8) memiliki risiko fatigue yang lebih rendah. Elemen gamifikasi terbukti efektif dalam mempertahankan konsistensi aktivitas, dengan `gamification_balance` menjadi faktor penting dalam prediksi fatigue.

**Tabel 4.12: Distribusi Risiko Fatigue Berdasarkan Achievement Rate**

| Achievement Rate | Low Risk | Medium Risk | High Risk |
| ---------------- | -------- | ----------- | --------- |
| < 0.4            | 5.2%     | 38.6%       | 56.2%     |
| 0.4 - 0.6        | 12.3%    | 52.7%       | 35.0%     |
| 0.6 - 0.8        | 18.9%    | 54.1%       | 27.0%     |
| > 0.8            | 26.8%    | 53.7%       | 19.5%     |

**Tabel 4.13: Efektivitas Elemen Gamifikasi**

| Gamification Element | Impact on Consistency | Impact on Fatigue Risk | Effectiveness Score |
| -------------------- | --------------------- | ---------------------- | ------------------- |
| Achievement Badges   | +0.23                 | -0.18                  | 0.85                |
| Progress Tracking    | +0.19                 | -0.15                  | 0.78                |
| Leaderboards         | +0.15                 | -0.12                  | 0.72                |
| Point Systems        | +0.21                 | -0.16                  | 0.81                |
| Streak Counters      | +0.25                 | -0.20                  | 0.89                |

Hasil menunjukkan bahwa peningkatan achievement rate berkorelasi dengan penurunan proporsi mahasiswa dalam kategori high risk dan peningkatan proporsi dalam kategori low risk. Streak counters menunjukkan efektivitas tertinggi dalam meningkatkan konsistensi dan mengurangi risiko fatigue.

![Gamification Analysis](results/visualizations/gamification_analysis.png)

_Gambar 4.8: Analisis Dampak Gamification terhadap Risiko Fatigue_

### 4.2.8 Validasi Model dan Generalisasi

Berdasarkan comprehensive validation report, model menunjukkan generalisasi yang baik dengan performance yang konsisten across different validation strategies. Feature efficiency analysis menunjukkan bahwa model dengan 3 fitur consensus memberikan balance optimal antara simplicity dan accuracy.

**Tabel 4.14: Model Validation Summary**

| Validation Method | Best Model        | Accuracy | F1-Score | Robustness Score |
| ----------------- | ----------------- | -------- | -------- | ---------------- |
| 5-Fold CV         | Gradient Boosting | 95.68%   | 0.9574   | 0.94             |
| SHAP Ablation     | Gradient Boosting | 95.88%   | 0.9520   | 0.92             |
| RFE Analysis      | Random Forest     | 95.68%   | 0.9572   | 0.93             |
| Consensus (Top 3) | Gradient Boosting | 95.88%   | 0.9520   | 0.95             |

Model terbaik (Gradient Boosting dengan 3 fitur consensus) menunjukkan robustness score tertinggi (0.95) dan konsistensi performa across semua metode validasi, menjadikannya pilihan optimal untuk implementasi praktis dalam prediksi risiko fatigue mahasiswa.

![Feature Efficiency](results/comprehensive_feature_validation/feature_efficiency.png)

_Gambar 4.9: Analisis Efisiensi Fitur dan Model Validation Summary_
