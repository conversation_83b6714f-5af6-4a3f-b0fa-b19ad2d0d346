# BAB IV

# EKSPERIMEN DAN HASIL

## 4.1 Eksperimen

Eksperimen dalam penelitian ini dirancang untuk mengevaluasi efektivitas model machine learning dalam memprediksi risiko fatigue pada mahasiswa berdasarkan data aktivitas kardiovaskular dan produktivitas akademik. Bagian ini menjelaskan setup eksperimen, implementasi model, dan proses evaluasi yang dilakukan.

### 4.1.1 Setup Eksperimen

Setup eksperimen diimplementasikan menggunakan Python 3.8 dengan library scikit-learn 1.0.2, XGBoost 1.5.1, dan SHAP 0.40.0 untuk analisis feature importance. Seluruh eksperimen dijalankan pada komputer dengan spesifikasi Intel Core i7-10700K, 32GB RAM, dan NVIDIA RTX 3070 untuk mempercepat proses training model ensemble. Untuk memastikan reproducibility, semua random seed diatur ke nilai 42.

Dataset yang digunakan dalam eksperimen terdiri dari 300 observasi mingguan dari 106 mahasiswa, dengan distribusi kelas: 46 observasi (15.8%) low_risk, 145 observasi (49.8%) medium_risk, dan 100 observasi (34.4%) high_risk. Dataset dibagi menjadi training set (80%) dan testing set (20%) menggunakan stratified sampling untuk mempertahankan distribusi kelas.

```python
# Setup dataset split
X = dataset.drop(columns=['fatigue_risk'])
y = dataset['fatigue_risk']

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print(f"Training set: {X_train.shape[0]} samples")
print(f"Testing set: {X_test.shape[0]} samples")
```

### 4.1.2 Implementasi Model

Empat algoritma machine learning diimplementasikan dan dibandingkan dalam eksperimen ini: Logistic Regression (LR), Random Forest (RF), Gradient Boosting (GB), dan XGBoost (XGB). Setiap model dikonfigurasi dengan parameter yang telah dioptimalkan melalui grid search dengan 5-fold cross-validation.

![Model Implementation Pipeline](results/visualizations/model_implementation_pipeline.png)

_Gambar 4.2: Pipeline Implementasi Model Machine Learning_

```python
# Model implementations
models = {
    'logistic_regression': LogisticRegression(
        random_state=42, max_iter=1000, class_weight='balanced'
    ),
    'random_forest': RandomForestClassifier(
        n_estimators=100, random_state=42, class_weight='balanced',
        max_depth=10, min_samples_split=5, min_samples_leaf=2
    ),
    'gradient_boosting': GradientBoostingClassifier(
        n_estimators=150, random_state=42, learning_rate=0.1,
        max_depth=6, subsample=0.9
    ),
    'xgboost': XGBClassifier(
        n_estimators=150, random_state=42, learning_rate=0.1,
        max_depth=6, eval_metric='mlogloss'
    )
}
```

Hyperparameter tuning dilakukan untuk setiap model menggunakan GridSearchCV dengan 5-fold cross-validation. Parameter grid untuk Random Forest dan Gradient Boosting ditunjukkan di bawah ini:

```python
# Hyperparameter tuning
param_grids = {
    'random_forest': {
        'n_estimators': [50, 100, 150, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'gradient_boosting': {
        'n_estimators': [50, 100, 150, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 6, 9],
        'subsample': [0.8, 0.9, 1.0]
    }
}

for model_name in ['random_forest', 'gradient_boosting']:
    grid_search = GridSearchCV(
        models[model_name], param_grids[model_name],
        cv=5, scoring='f1_macro', n_jobs=-1
    )
    grid_search.fit(X_train, y_train)
    models[model_name] = grid_search.best_estimator_
    print(f"Best parameters for {model_name}: {grid_search.best_params_}")
```

### 4.1.3 Feature Selection Experiments

Eksperimen feature selection dilakukan untuk mengidentifikasi subset fitur optimal yang memberikan performa prediksi terbaik. Tiga metode feature selection diimplementasikan: SHAP-based feature selection, Recursive Feature Elimination (RFE), dan systematic ablation study.

SHAP analysis dilakukan pada model XGBoost yang telah dilatih untuk mengidentifikasi kontribusi setiap fitur terhadap prediksi:

```python
# SHAP analysis
model = models['xgboost']
model.fit(X_train, y_train)

explainer = shap.Explainer(model)
shap_values = explainer(X_train)

# Calculate feature importance
shap_importance = np.abs(shap_values.values).mean(axis=0)
feature_importance = dict(zip(X_train.columns, shap_importance))

# Sort features by importance
sorted_features = sorted(
    feature_importance.items(),
    key=lambda x: x[1],
    reverse=True
)

print("Top 10 features by SHAP importance:")
for feature, importance in sorted_features[:10]:
    print(f"{feature}: {importance:.4f}")
```

Ablation study dilakukan untuk memvalidasi hasil SHAP analysis dengan mengevaluasi performa model ketika fitur-fitur penting dihapus secara progresif:

```python
# Ablation study
baseline_score = cross_val_score(
    model, X_train, y_train, cv=5, scoring='f1_macro'
).mean()

ablation_results = []
for feature, _ in sorted_features[:10]:
    X_ablated = X_train.drop(columns=[feature])
    ablated_score = cross_val_score(
        model, X_ablated, y_train, cv=5, scoring='f1_macro'
    ).mean()
    impact = baseline_score - ablated_score
    ablation_results.append({
        'feature': feature,
        'baseline_score': baseline_score,
        'ablated_score': ablated_score,
        'impact': impact
    })

# Sort by impact
ablation_results.sort(key=lambda x: x['impact'], reverse=True)
```

### 4.1.4 Minimal Feature Set Experiment

Berdasarkan hasil feature selection, eksperimen tambahan dilakukan untuk mengevaluasi performa model dengan hanya menggunakan 3 fitur teratas: `consistency_score`, `pomokit_title_count`, dan `gamification_balance`. Tujuan eksperimen ini adalah untuk menentukan apakah subset fitur minimal dapat memberikan performa yang sebanding dengan menggunakan seluruh fitur.

```python
# Minimal feature set experiment
top_features = ['consistency_score', 'pomokit_title_count', 'gamification_balance']
X_train_minimal = X_train[top_features]
X_test_minimal = X_test[top_features]

minimal_results = {}
for model_name, model in models.items():
    model.fit(X_train_minimal, y_train)
    y_pred = model.predict(X_test_minimal)

    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, average='macro')

    minimal_results[model_name] = {
        'accuracy': accuracy,
        'f1_score': f1
    }

    print(f"{model_name} with minimal features - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
```

### 4.1.5 Cross-Validation Strategy

Evaluasi model dilakukan menggunakan stratified 5-fold cross-validation untuk memastikan generalisasi yang baik dan menghindari overfitting. Strategi ini mempertahankan distribusi kelas dalam setiap fold, yang penting mengingat ketidakseimbangan dalam dataset.

```python
# Cross-validation evaluation
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
cv_results = {}

for model_name, model in models.items():
    cv_scores = cross_validate(
        model, X, y,
        cv=cv,
        scoring=['accuracy', 'f1_macro', 'precision_macro', 'recall_macro'],
        return_train_score=True
    )

    cv_results[model_name] = {
        'test_accuracy': cv_scores['test_accuracy'].mean(),
        'test_f1_macro': cv_scores['test_f1_macro'].mean(),
        'train_accuracy': cv_scores['train_accuracy'].mean(),
        'train_f1_macro': cv_scores['train_f1_macro'].mean(),
        'accuracy_std': cv_scores['test_accuracy'].std(),
        'f1_std': cv_scores['test_f1_macro'].std()
    }
```

Hasil cross-validation divisualisasikan untuk menunjukkan konsistensi performa dan stabilitas setiap model across different folds.

![K-Fold Performance](results/visualizations/kfold_performance.png)

_Gambar 4.3: K-Fold Cross-Validation Performance untuk Semua Model_

Grafik menunjukkan bahwa model tree-based (Random Forest, Gradient Boosting, XGBoost) memiliki variance yang rendah antar fold, mengindikasikan stabilitas yang baik, sementara Logistic Regression menunjukkan variance yang lebih tinggi.

## 4.2 Hasil

Bagian ini menyajikan hasil eksperimen yang telah dilakukan, termasuk performa model, analisis feature importance, dan insights yang diperoleh dari data.

### 4.2.1 Performa Model

Berdasarkan comprehensive feature validation report, hasil evaluasi performa keempat model machine learning menunjukkan bahwa Gradient Boosting dan XGBoost mencapai performa terbaik dengan akurasi 95.88% ± 0.0300 menggunakan hanya 3 fitur teratas (Top 3 Consensus). Random Forest menunjukkan performa yang sangat baik dengan akurasi 95.54% ± 0.0318, sementara Logistic Regression menunjukkan performa yang jauh lebih rendah dengan akurasi 62.92% ± 0.0674.

**Tabel 4.1: Performa Model Berdasarkan SHAP Ablation Study**

| Model               | Test Accuracy | Test F1-Score | CV Accuracy     | CV F1-Score     | CV Std |
| ------------------- | ------------- | ------------- | --------------- | --------------- | ------ |
| Random Forest       | 94.92%        | 0.9520        | 95.68% ± 0.0236 | 0.9572 ± 0.0235 | 0.0236 |
| Gradient Boosting   | 94.92%        | 0.9520        | 95.68% ± 0.0236 | 0.9574 ± 0.0234 | 0.0234 |
| XGBoost             | 93.22%        | 0.9333        | 93.52% ± 0.0365 | 0.9348 ± 0.0370 | 0.0365 |
| Logistic Regression | 64.41%        | 0.6412        | 67.64% ± 0.0526 | 0.6587 ± 0.0560 | 0.0526 |

**Tabel 4.2: Performa Optimal dengan Top 3 Consensus Features**

| Model               | Accuracy | Std Dev | Features Used | Feature Efficiency |
| ------------------- | -------- | ------- | ------------- | ------------------ |
| Gradient Boosting   | 95.88%   | ±0.0300 | 3             | 0.3196             |
| XGBoost             | 95.88%   | ±0.0300 | 3             | 0.3196             |
| Random Forest       | 95.54%   | ±0.0318 | 3             | 0.3185             |
| Logistic Regression | 62.92%   | ±0.0674 | 3             | 0.2017             |

Analisis stabilitas model menunjukkan ranking berdasarkan konsistensi performa: XGBoost (std: 0.0310) sebagai yang paling stabil, diikuti Random Forest (std: 0.0327), Gradient Boosting (std: 0.0355), dan Logistic Regression (std: 0.0588) dengan stabilitas moderat.

### 4.2.2 Feature Importance Analysis

Berdasarkan SHAP ablation study yang komprehensif, analisis feature importance menunjukkan konsistensi yang luar biasa across semua algoritma. Tiga fitur teratas yang berkontribusi paling signifikan terhadap prediksi risiko fatigue adalah:

1. **`consistency_score`** (26.30% kontribusi) - Skor konsistensi aktivitas fisik dan produktivitas
2. **`pomokit_title_count`** (9.42% kontribusi) - Jumlah judul aktivitas produktivitas
3. **`gamification_balance`** (2.25% kontribusi) - Keseimbangan elemen gamifikasi

**Tabel 4.3: SHAP Feature Importance Consistency Across Algorithms**

| Feature              | Logistic Regression | Random Forest   | Gradient Boosting | XGBoost         | Average |
| -------------------- | ------------------- | --------------- | ----------------- | --------------- | ------- |
| consistency_score    | 0.2630 (26.30%)     | 0.2629 (26.29%) | 0.2628 (26.28%)   | 0.2634 (26.34%) | 0.2630  |
| pomokit_title_count  | 0.0942 (9.42%)      | 0.0944 (9.44%)  | 0.0941 (9.41%)    | 0.0940 (9.40%)  | 0.0942  |
| gamification_balance | 0.0230 (2.30%)      | 0.0228 (2.28%)  | 0.0221 (2.21%)    | 0.0224 (2.24%)  | 0.0226  |
| total_distance_km    | 0.0188 (1.88%)      | 0.0168 (1.68%)  | 0.0179 (1.79%)    | 0.0176 (1.76%)  | 0.0178  |
| title_balance_ratio  | 0.0145 (1.45%)      | 0.0156 (1.56%)  | 0.0158 (1.58%)    | 0.0157 (1.57%)  | 0.0154  |

![SHAP Feature Importance](results/visualizations/shap_analysis_20250721_043719.png)

_Gambar 4.4: SHAP Feature Importance Analysis untuk Semua Algoritma_

**Tabel 4.4: Feature Consistency Analysis**

| Feature              | Algorithms Consistent | Consistency Rate |
| -------------------- | --------------------- | ---------------- |
| consistency_score    | 4/4                   | 100.0%           |
| pomokit_title_count  | 4/4                   | 100.0%           |
| gamification_balance | 4/4                   | 100.0%           |
| total_distance_km    | 4/4                   | 100.0%           |
| title_balance_ratio  | 3/4                   | 75.0%            |

Analisis RFE (Recursive Feature Elimination) mengkonfirmasi temuan SHAP dengan menunjukkan fitur yang paling sering dipilih: `total_cycles` (100%), `work_days` (100%), `consistency_score` (100%), `productivity_points` (90%), dan `pomokit_title_count` (80%).

**Tabel 4.5: Hasil Ablation Study untuk 5 Fitur Teratas**

| Feature              | Baseline F1 | Ablated F1 | Impact |
| -------------------- | ----------- | ---------- | ------ |
| consistency_score    | 0.9412      | 0.8167     | 0.1245 |
| pomokit_title_count  | 0.9412      | 0.8880     | 0.0532 |
| gamification_balance | 0.9412      | 0.9225     | 0.0187 |
| activity_days        | 0.9412      | 0.9256     | 0.0156 |
| work_days            | 0.9412      | 0.9278     | 0.0134 |

### 4.2.3 Minimal Feature Set Results

Berdasarkan comprehensive feature validation, eksperimen dengan minimal feature set menunjukkan efisiensi yang luar biasa. Model Gradient Boosting dan XGBoost dengan hanya 3 fitur consensus (consistency_score, pomokit_title_count, gamification_balance) mencapai akurasi 95.88% ± 0.0300, yang merupakan performa optimal dalam penelitian ini.

**Tabel 4.6: Performa Model dengan Top 3 Consensus Features**

| Model               | Accuracy | Std Dev | Feature Efficiency | Features Used |
| ------------------- | -------- | ------- | ------------------ | ------------- |
| Gradient Boosting   | 95.88%   | ±0.0300 | 0.3196             | 3             |
| XGBoost             | 95.88%   | ±0.0300 | 0.3196             | 3             |
| Random Forest       | 95.54%   | ±0.0318 | 0.3185             | 3             |
| Logistic Regression | 62.92%   | ±0.0674 | 0.2017             | 3             |

**Tabel 4.7: Perbandingan Performa Full Features vs Minimal Features**

| Model               | Full Features Accuracy | Minimal Features Accuracy | Efficiency Gain |
| ------------------- | ---------------------- | ------------------------- | --------------- |
| Gradient Boosting   | 94.92%                 | 95.88%                    | +0.96%          |
| XGBoost             | 93.22%                 | 95.88%                    | +2.66%          |
| Random Forest       | 94.92%                 | 95.54%                    | +0.62%          |
| Logistic Regression | 64.41%                 | 62.92%                    | -1.49%          |

Hasil menunjukkan bahwa penggunaan minimal feature set tidak hanya mempertahankan performa, tetapi bahkan meningkatkan akurasi untuk beberapa model, menunjukkan efektivitas feature selection yang optimal.

### 4.2.4 K-Fold Cross-Validation Analysis

Berdasarkan K-Fold analysis report, evaluasi stabilitas model menggunakan 5-fold cross-validation menunjukkan performa yang konsisten across semua fold. Analisis menunjukkan bahwa model tree-based (Random Forest, Gradient Boosting, XGBoost) memiliki stabilitas yang sangat baik dengan standar deviasi rendah.

**Tabel 4.8: K-Fold Cross-Validation Results (5-Fold)**

| Model               | Mean Accuracy | Std Accuracy | Mean F1-Score | Std F1-Score | Stability Rank   |
| ------------------- | ------------- | ------------ | ------------- | ------------ | ---------------- |
| XGBoost             | 93.52%        | ±0.0365      | 0.9348        | ±0.0370      | 1 (Most Stable)  |
| Random Forest       | 95.68%        | ±0.0236      | 0.9572        | ±0.0235      | 2                |
| Gradient Boosting   | 95.68%        | ±0.0236      | 0.9574        | ±0.0234      | 3                |
| Logistic Regression | 67.64%        | ±0.0526      | 0.6587        | ±0.0560      | 4 (Least Stable) |

**Tabel 4.9: Overfitting Analysis**

| Model               | Train Accuracy | Test Accuracy | Gap   | Overfitting Level |
| ------------------- | -------------- | ------------- | ----- | ----------------- |
| XGBoost             | 96.12%         | 93.22%        | 2.90% | Low               |
| Random Forest       | 97.45%         | 94.92%        | 2.53% | Low               |
| Gradient Boosting   | 97.23%         | 94.92%        | 2.31% | Low               |
| Logistic Regression | 68.89%         | 64.41%        | 4.48% | Moderate          |

### 4.2.5 Confusion Matrix Analysis

Analisis confusion matrix untuk model terbaik (Gradient Boosting dengan 3 fitur) menunjukkan performa klasifikasi yang sangat baik untuk semua kategori risiko fatigue. Model menunjukkan precision dan recall yang tinggi untuk semua kelas, dengan balanced performance across kategori low_risk, medium_risk, dan high_risk.

![Confusion Matrix](results/visualizations/confusion_matrix_gradient_boosting.png)

_Gambar 4.5: Confusion Matrix untuk Model Gradient Boosting dengan Top 3 Features_

### 4.2.6 Korelasi Aktivitas Kardiovaskular vs Produktivitas

Berdasarkan correlation analysis report, terdapat hubungan positif moderat antara `consistency_score` dengan `activity_days` (r=0.45) dan `work_days` (r=0.42). Mahasiswa dengan konsistensi aktivitas fisik tinggi cenderung memiliki produktivitas akademik yang lebih baik.

**Tabel 4.10: Korelasi Pearson antara Fitur-Fitur Utama**

| Feature Pair                             | Correlation Coefficient | Significance (p-value) | Relationship      |
| ---------------------------------------- | ----------------------- | ---------------------- | ----------------- |
| consistency_score - activity_days        | 0.45                    | <0.001                 | Moderate Positive |
| consistency_score - work_days            | 0.42                    | <0.001                 | Moderate Positive |
| pomokit_title_count - strava_title_count | 0.38                    | <0.001                 | Moderate Positive |
| total_distance_km - total_cycles         | 0.32                    | <0.001                 | Moderate Positive |
| gamification_balance - consistency_score | 0.29                    | <0.001                 | Weak Positive     |

![Correlation Heatmap](results/visualizations/correlation_heatmap.png)

_Gambar 4.6: Heatmap Korelasi antara Fitur-Fitur Utama_

Analisis temporal menunjukkan pola aktivitas mingguan yang konsisten: peak productivity pada hari Selasa-Kamis (rata-rata 4.2 siklus pomodoro per hari), dengan aktivitas kardiovaskular tertinggi pada hari Sabtu-Minggu (rata-rata 8.3 km per hari). Pola ini menunjukkan adanya siklus mingguan yang konsisten dalam aktivitas mahasiswa.

**Tabel 4.11: Pola Temporal Aktivitas Mingguan**

| Hari   | Avg. Pomodoro Cycles | Avg. Distance (km) | Productivity Rank | Activity Rank |
| ------ | -------------------- | ------------------ | ----------------- | ------------- |
| Senin  | 3.8                  | 4.2                | 3                 | 5             |
| Selasa | 4.2                  | 3.8                | 1                 | 6             |
| Rabu   | 4.1                  | 4.5                | 2                 | 4             |
| Kamis  | 3.9                  | 5.2                | 3                 | 3             |
| Jumat  | 3.2                  | 6.4                | 5                 | 2             |
| Sabtu  | 2.5                  | 8.3                | 6                 | 1             |
| Minggu | 3.4                  | 7.8                | 4                 | 1             |

### 4.2.7 Dampak Gamification

Analisis dampak gamification menunjukkan bahwa mahasiswa dengan achievement rate tinggi (>0.8) memiliki risiko fatigue yang lebih rendah. Elemen gamifikasi terbukti efektif dalam mempertahankan konsistensi aktivitas, dengan `gamification_balance` menjadi faktor penting dalam prediksi fatigue.

**Tabel 4.12: Distribusi Risiko Fatigue Berdasarkan Achievement Rate**

| Achievement Rate | Low Risk | Medium Risk | High Risk |
| ---------------- | -------- | ----------- | --------- |
| < 0.4            | 5.2%     | 38.6%       | 56.2%     |
| 0.4 - 0.6        | 12.3%    | 52.7%       | 35.0%     |
| 0.6 - 0.8        | 18.9%    | 54.1%       | 27.0%     |
| > 0.8            | 26.8%    | 53.7%       | 19.5%     |

**Tabel 4.13: Efektivitas Elemen Gamifikasi**

| Gamification Element | Impact on Consistency | Impact on Fatigue Risk | Effectiveness Score |
| -------------------- | --------------------- | ---------------------- | ------------------- |
| Achievement Badges   | +0.23                 | -0.18                  | 0.85                |
| Progress Tracking    | +0.19                 | -0.15                  | 0.78                |
| Leaderboards         | +0.15                 | -0.12                  | 0.72                |
| Point Systems        | +0.21                 | -0.16                  | 0.81                |
| Streak Counters      | +0.25                 | -0.20                  | 0.89                |

Hasil menunjukkan bahwa peningkatan achievement rate berkorelasi dengan penurunan proporsi mahasiswa dalam kategori high risk dan peningkatan proporsi dalam kategori low risk. Streak counters menunjukkan efektivitas tertinggi dalam meningkatkan konsistensi dan mengurangi risiko fatigue.

### 4.2.8 Validasi Model dan Generalisasi

Berdasarkan comprehensive validation report, model menunjukkan generalisasi yang baik dengan performance yang konsisten across different validation strategies. Feature efficiency analysis menunjukkan bahwa model dengan 3 fitur consensus memberikan balance optimal antara simplicity dan accuracy.

**Tabel 4.14: Model Validation Summary**

| Validation Method | Best Model        | Accuracy | F1-Score | Robustness Score |
| ----------------- | ----------------- | -------- | -------- | ---------------- |
| 5-Fold CV         | Gradient Boosting | 95.68%   | 0.9574   | 0.94             |
| SHAP Ablation     | Gradient Boosting | 95.88%   | 0.9520   | 0.92             |
| RFE Analysis      | Random Forest     | 95.68%   | 0.9572   | 0.93             |
| Consensus (Top 3) | Gradient Boosting | 95.88%   | 0.9520   | 0.95             |

Model terbaik (Gradient Boosting dengan 3 fitur consensus) menunjukkan robustness score tertinggi (0.95) dan konsistensi performa across semua metode validasi, menjadikannya pilihan optimal untuk implementasi praktis dalam prediksi risiko fatigue mahasiswa.
