# BAB III

# METODOLOGI PENELITIAN

## 3.1 Desain Penelitian

Penelitian ini menggunakan **desain cross-sectional dengan elemen longitudinal terbatas** untuk menganalisis hubungan antara aktivitas kardiovaskular dan produktivitas akademik dalam prediksi risiko fatigue pada mahasiswa. Pendekatan **observational study** dipilih untuk menangkap pola perilaku natural mahasiswa tanpa intervensi yang dapat mempengaruhi validitas data.

Penelitian ini menerapkan **mixed-methods approach** yang mengkombinasikan:

-   **Quantitative analysis** untuk data aktivitas fisik dan produktivitas
-   **Text-based analysis** untuk analisis judul aktivitas
-   **Machine learning modeling** untuk prediksi risiko fatigue
-   **Ablation study methodology** untuk validasi feature importance

## 3.2 Alur Metodologi Penelitian

Berdasarkan diagram alur metodologi yang telah disusun, penelitian ini mengikuti tahapan sistematis sebagai berikut:

![Diagram Alur Metodologi](diagram%20alur%20metodologi.png)

### 3.2.1 Tahapan <PERSON>tama Peneli<PERSON>

**1. Data Collection** → **2. Data Preprocessing** → **3. Feature Engineering** → **4. Labeling Strategy** → **5. Feature Selection** → **6. Training Model** → **7. Evaluation Model** → **8. Results & Analysis**

### 3.2.2 Deskripsi Setiap Tahapan

#### **Tahap 1: Data Collection**

**Sumber Data dan Populasi:**

-   **Populasi Target**: Mahasiswa Indonesia yang aktif menggunakan teknologi digital untuk monitoring aktivitas fisik dan produktivitas akademik
-   **Total Partisipan**: 106 mahasiswa
-   **Total Observasi**: 300 observasi mingguan
-   **Unit Analisis**: Data agregasi mingguan per mahasiswa

**Kriteria Inklusi:**

1. Mahasiswa aktif di perguruan tinggi Indonesia
2. Pengguna aktif platform Strava untuk tracking aktivitas kardiovaskular
3. Pengguna aktif aplikasi Pomokit untuk manajemen produktivitas
4. Memiliki data aktivitas minimal 4 minggu berturut-turut
5. Literasi teknologi yang memadai untuk penggunaan aplikasi tracking

**Kriteria Eksklusi:**

1. Data aktivitas yang tidak lengkap atau tidak konsisten
2. Pengguna dengan kondisi medis yang mempengaruhi aktivitas fisik
3. Data dengan indikasi manipulasi atau tidak valid

**Platform Strava (Aktivitas Kardiovaskular):**

-   **Jenis Data**: Jarak tempuh aktivitas (km), durasi aktivitas (menit), frekuensi aktivitas per minggu, judul/deskripsi aktivitas, tanggal dan waktu aktivitas
-   **Metrik yang Diekstrak**:
    -   `total_distance_km`: Total jarak tempuh mingguan
    -   `avg_distance_km`: Rata-rata jarak per aktivitas
    -   `total_time_minutes`: Total durasi aktivitas mingguan
    -   `avg_time_minutes`: Rata-rata durasi per aktivitas
    -   `activity_days`: Jumlah hari aktif per minggu
    -   `strava_title_count`: Jumlah aktivitas dengan judul
    -   `strava_title_length`: Rata-rata panjang judul aktivitas
    -   `strava_unique_words`: Keragaman kata dalam judul

**Platform Pomokit (Produktivitas Akademik):**

-   **Jenis Data**: Siklus pomodoro yang diselesaikan, durasi sesi kerja, judul/deskripsi tugas, tanggal dan waktu sesi kerja, achievement rate dan gamification metrics
-   **Metrik yang Diekstrak**:
    -   `total_cycles`: Total siklus pomodoro mingguan
    -   `work_days`: Jumlah hari kerja per minggu
    -   `productivity_points`: Poin produktivitas yang diperoleh
    -   `pomokit_title_count`: Jumlah sesi dengan judul
    -   `pomokit_title_length`: Rata-rata panjang judul tugas
    -   `pomokit_unique_words`: Keragaman kata dalam judul tugas
    -   `achievement_rate`: Tingkat pencapaian target
    -   `gamification_balance`: Keseimbangan elemen gamifikasi

**Proses Pengumpulan Data:**

```python
def load_raw_data(self):
    # Load Strava data
    strava_data = pd.read_csv('dataset/raw/strava.csv')

    # Load Pomokit data
    pomokit_data = pd.read_csv('dataset/raw/pomokit.csv')

    return strava_data, pomokit_data
```

**Aspek Etis dan Privasi:**

-   **User ID anonymization** menggunakan hash functions
-   **Removal of personally identifiable information**
-   **Aggregated data analysis** untuk melindungi privasi individual
-   **Secure data storage** dengan enkripsi
-   **Access control** untuk data sensitif
-   **Compliance** dengan regulasi privasi data

#### **Tahap 2: Data Preprocessing**

**Data Cleaning Process:**

**Strava Data Cleaning:**

```python
# Pembersihan data jarak dan waktu
df['distance'] = df['distance'].str.replace(' km', '').str.replace(',', '.')
df['moving_time'] = convert_time_to_minutes(df['moving_time'])

# Pembersihan data tanggal
df['date'] = pd.to_datetime(df['date'], errors='coerce')
df = df.dropna(subset=['date'])
```

**Pomokit Data Cleaning:**

```python
# Pembersihan data siklus dan tanggal
df['cycle'] = pd.to_numeric(df['cycle'], errors='coerce')
df['date'] = pd.to_datetime(df['date'], errors='coerce')
df = df.dropna(subset=['date'])
```

**Weekly Aggregation Process:**
Data dari kedua platform diagregasi menjadi data mingguan untuk setiap partisipan:

```python
def create_weekly_aggregation(strava_data, pomokit_data):
    # Agregasi data Strava per minggu
    strava_weekly = strava_data.groupby(['user_id', 'year_week']).agg({
        'distance': ['sum', 'mean', 'count'],
        'moving_time': ['sum', 'mean'],
        'title': ['count', lambda x: len(' '.join(x.astype(str)).split())]
    })

    # Agregasi data Pomokit per minggu
    pomokit_weekly = pomokit_data.groupby(['user_id', 'year_week']).agg({
        'cycle': ['sum', 'count'],
        'title': ['count', lambda x: len(' '.join(x.astype(str)).split())]
    })

    # Merge kedua dataset
    weekly_data = pd.merge(strava_weekly, pomokit_weekly,
                          on=['user_id', 'year_week'], how='outer')
    return weekly_data
```

#### **Tahap 3: Feature Engineering**

**Derived Features Creation:**

**1. Consistency Score:**

```python
consistency_score = (activity_days / 7) * (work_days / 7)
```

**2. Gamification Balance:**

```python
gamification_balance = activity_points + productivity_points
```

**3. Title Balance Ratio:**

```python
title_balance_ratio = pomokit_title_count / (strava_title_count + pomokit_title_count)
```

**4. Total Title Diversity:**

```python
total_title_diversity = strava_unique_words + pomokit_unique_words
```

**Feature Engineering Process:**

```python
def _calculate_derived_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
    df = data.copy()

    # Calculate consistency score
    physical_consistency = (df['activity_days'].clip(upper=2)) / 2
    work_consistency = (df['work_days'].clip(upper=5)) / 5
    df['consistency_score'] = (physical_consistency + work_consistency) / 2

    # Calculate efficiency metrics
    df['weekly_efficiency'] = np.where(
        df['work_days'] == 0,
        np.nan,
        df['total_cycles'] / df['work_days']
    )

    return df
```

#### **Tahap 4: Labeling Strategy**

**Fatigue Risk Score Calculation:**

Skor risiko fatigue dihitung menggunakan formula multi-dimensional:

```python
def calculate_fatigue_risk_score(df):
    fatigue_score = (
        # Workload factors (40% weight)
        (df['work_days'] / 7) * 20 +
        (df['work_intensity'].clip(upper=5) / 5) * 15 +
        (df['work_life_imbalance'].clip(upper=10) / 10) * 12 +
        df['activity_deficit'] * 3 +
        df['consistency_deficit'] * 25 +

        # Recovery factors (10% weight - negative contribution)
        - df['recovery_count'] * 8 -
        - (df['activity_days'] / 7) * 8
    )

    # Normalize to 0-100 scale
    df['fatigue_risk_score'] = np.clip(fatigue_score, 0, 100)
    return df
```

**Risk Category Classification:**

```python
def classify_fatigue_risk(score):
    if score <= 30:
        return 'low_risk'
    elif score <= 60:
        return 'medium_risk'
    else:
        return 'high_risk'
```

**Distribusi Kategori:**

-   **Low Risk**: ≤ 30 (15.8% - 46 mahasiswa)
-   **Medium Risk**: 31-60 (49.8% - 145 mahasiswa)
-   **High Risk**: > 60 (34.4% - 100 mahasiswa)

#### **Tahap 5: Feature Selection**

**SHAP-based Feature Selection:**

SHAP (SHapley Additive exPlanations) digunakan untuk mengidentifikasi feature importance:

```python
def calculate_shap_importance(model, X, feature_names):
    explainer = shap.Explainer(model)
    shap_values = explainer(X)

    # Calculate mean absolute SHAP values
    shap_importance = np.abs(shap_values.values).mean(axis=0)

    return dict(zip(feature_names, shap_importance))
```

**Recursive Feature Elimination (RFE):**

```python
def perform_rfe_analysis(model, X, y, feature_names):
    rfe = RFE(model, n_features_to_select=1, step=1)
    rfe.fit(X, y)

    # Get feature rankings
    feature_rankings = dict(zip(feature_names, rfe.ranking_))

    return feature_rankings
```

**Systematic Ablation Study:**

Progressive Feature Removal untuk validasi feature importance:

```python
def ablation_study(model, X, y, feature_names):
    baseline_score = cross_val_score(model, X, y, cv=5).mean()

    ablation_results = []
    for feature in feature_names:
        # Remove feature
        X_ablated = X.drop(columns=[feature])

        # Calculate performance
        ablated_score = cross_val_score(model, X_ablated, y, cv=5).mean()

        # Calculate impact
        impact = baseline_score - ablated_score

        ablation_results.append({
            'feature': feature,
            'baseline_score': baseline_score,
            'ablated_score': ablated_score,
            'impact': impact
        })

    return ablation_results
```

**Feature Filtering untuk Mencegah Data Leakage:**

```python
class FeatureFilter:
    def __init__(self):
        # Features yang digunakan untuk membuat label (TIDAK BOLEH untuk model)
        self.label_creation_features = {
            'fatigue_risk_score', 'fatigue_risk',
            'stress_count', 'workload_count', 'negative_emotion_count'
        }

        # Features yang AMAN untuk model ML
        self.safe_model_features = {
            'total_distance_km', 'avg_distance_km', 'total_time_minutes',
            'activity_days', 'consistency_score', 'gamification_balance'
        }
```

#### **Tahap 6: Training Model**

**Algoritma yang Digunakan:**

**1. Logistic Regression**

```python
LogisticRegression(
    random_state=42,
    max_iter=1000,
    class_weight='balanced'
)
```

**2. Random Forest**

```python
RandomForestClassifier(
    n_estimators=100,
    random_state=42,
    class_weight='balanced',
    max_depth=10
)
```

**3. Gradient Boosting**

```python
GradientBoostingClassifier(
    n_estimators=100,
    random_state=42,
    learning_rate=0.1,
    max_depth=6
)
```

**4. XGBoost**

```python
XGBClassifier(
    n_estimators=100,
    random_state=42,
    learning_rate=0.1,
    max_depth=6,
    eval_metric='mlogloss'
)
```

**Hyperparameter Tuning Process:**

Optimasi hyperparameter menggunakan GridSearchCV dengan 5-fold cross-validation:

```python
param_grids = {
    'random_forest': {
        'n_estimators': [50, 100, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'gradient_boosting': {
        'n_estimators': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 6, 9],
        'subsample': [0.8, 0.9, 1.0]
    }
}
```

#### **Tahap 7: Evaluation Model**

**Cross-Validation Strategy:**

Stratified K-Fold Cross-Validation (k=5) untuk mempertahankan distribusi kelas:

```python
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
cv_scores = cross_validate(
    model, X, y,
    cv=cv,
    scoring=['accuracy', 'f1_macro', 'precision_macro', 'recall_macro'],
    return_train_score=True
)
```

**Metrik Evaluasi:**

**Primary Metrics:**

-   **Accuracy**: Proporsi prediksi yang benar
-   **F1-Score (Macro)**: Harmonic mean precision dan recall
-   **Precision (Macro)**: Rata-rata precision per kelas
-   **Recall (Macro)**: Rata-rata recall per kelas

**Secondary Metrics:**

-   **ROC-AUC**: Area under ROC curve
-   **Confusion Matrix**: Matriks klasifikasi detail
-   **Classification Report**: Laporan per-kelas

**Overfitting Detection:**

Train-Validation Gap Analysis:

```python
def detect_overfitting(train_scores, val_scores):
    train_mean = np.mean(train_scores)
    val_mean = np.mean(val_scores)
    gap = train_mean - val_mean

    if gap > 0.1:
        return "HIGH_OVERFITTING"
    elif gap > 0.05:
        return "MODERATE_OVERFITTING"
    else:
        return "LOW_OVERFITTING"
```

#### **Tahap 8: Results & Analysis**

**Analisis Hasil:**

-   **Interpretasi performa model terbaik** berdasarkan multiple metrics
-   **Analisis feature importance** dan kontribusi setiap variabel menggunakan SHAP
-   **Validasi hipotesis penelitian** melalui statistical testing
-   **Identifikasi pattern dan insights** dari data untuk aplikasi praktis

**Visualisasi dan Reporting:**

-   **Confusion matrix dan ROC curves** untuk evaluasi klasifikasi
-   **Feature importance plots** untuk interpretability model
-   **Performance comparison charts** antar algoritma
-   **Time series analysis** untuk pola temporal aktivitas
-   **Comprehensive research report** dengan academic standards

**Output Penelitian:**

-   **Model terbaik** dengan akurasi optimal untuk prediksi fatigue
-   **Feature ranking** berdasarkan importance scores dari SHAP analysis
-   **Insights praktis** untuk aplikasi real-world dalam student wellness
-   **Rekomendasi** untuk penelitian lanjutan dan implementasi sistem

**Complete Analysis Pipeline:**

```python
class CompleteAnalysisPipeline:
    def __init__(self, include_ml=True):
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()
        self.fatigue_classifier = FatigueRiskClassifier()

    def run_complete_pipeline(self):
        # Phase 1: Data Processing
        processed_data = self.run_data_processing()

        # Phase 2: Fatigue Prediction
        ml_results = self.run_fatigue_prediction(processed_data)

        # Phase 3: Visualization
        self.create_research_visualizations(processed_data)

        return {'ml_results': ml_results, 'processed_data': processed_data}
```

**Pipeline Modes:**

1. **Complete Pipeline**: Data Processing + Fatigue Prediction + ML Analysis
2. **Fatigue Only**: Fatigue Classification + Feature Selection + ML
3. **No ML**: Data Processing only
4. **ML Only**: Machine Learning pipeline only

**Bias Correction Framework:**

**Language Pattern Bias Correction:**

-   Normalisasi untuk perbedaan bahasa dalam judul aktivitas
-   Standardisasi format waktu dan jarak
-   Koreksi untuk variasi cultural dalam deskripsi aktivitas

**Activity Type Bias Correction:**

-   Normalisasi untuk berbagai jenis aktivitas kardiovaskular
-   Standardisasi metrik across different activity types
-   Koreksi untuk seasonal variations

**Keterbatasan Metodologi:**

**Keterbatasan Data:**

1. **Self-reported bias** dalam data platform digital
2. **Missing data** untuk aktivitas non-digital
3. **Temporal limitations** dalam periode observasi
4. **Platform dependency** pada akurasi sensor dan algoritma

**Keterbatasan Analisis:**

1. **Cross-sectional design** membatasi inferensi kausal
2. **Sample representativeness** terbatas pada pengguna teknologi
3. **Generalizability** terbatas pada konteks mahasiswa Indonesia
4. **Validation** terbatas pada internal dataset

**Keterbatasan Model:**

1. **Feature engineering** bergantung pada domain knowledge
2. **Model interpretability** terbatas untuk complex algorithms
3. **Overfitting risk** pada dataset kecil
4. **External validation** belum dilakukan

Metodologi ini dirancang untuk memberikan analisis yang komprehensif dan dapat direplikasi, dengan mempertimbangkan aspek teknis, etis, dan praktis dalam implementasi sistem prediksi fatigue berbasis machine learning.
