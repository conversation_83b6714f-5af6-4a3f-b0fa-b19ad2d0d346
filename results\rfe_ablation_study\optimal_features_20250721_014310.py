# Optimal Feature Set from RFE Analysis
# Generated on 2025-07-21 01:43:10
# Best Algorithm: XGBoost
# Accuracy: 0.9554 ± 0.0318

OPTIMAL_FEATURES = [
    'consistency_score',
    'productivity_points',
    'strava_title_count',
    'total_cycles',
    'work_days',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]

# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = 0.9554

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {
    'productivity_points': 0.0034,  # 0.36% impact
    'consistency_score': 0.0034,  # 0.36% impact
    'strava_title_count': -0.0034,  # -0.35% impact
    'total_cycles': 0.0000,  # 0.00% impact
    'work_days': 0.0000,  # 0.00% impact
}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
    'productivity_points': 0.00,  # 0.00%
    'consistency_score': 66.77,  # 66.77%
    'strava_title_count': 3.85,  # 3.85%
    'total_cycles': 28.91,  # 28.91%
    'work_days': 0.48,  # 0.48%
}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
    'productivity_points',  # 0.36% impact when removed
    'consistency_score',  # 0.36% impact when removed
    'strava_title_count',  # -0.35% impact when removed
    'total_cycles',  # 0.00% impact when removed
    'work_days',  # 0.00% impact when removed
]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
    'productivity_points',  # 0.00% permutation importance
    'consistency_score',  # 66.77% permutation importance
    'strava_title_count',  # 3.85% permutation importance
    'total_cycles',  # 28.91% permutation importance
    'work_days',  # 0.48% permutation importance
]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
    'productivity_points': 'MINIMAL - Negligible performance impact',
    'consistency_score': 'MINIMAL - Negligible performance impact',
    'strava_title_count': 'MINIMAL - Negligible performance impact',
    'total_cycles': 'MINIMAL - Negligible performance impact',
    'work_days': 'MINIMAL - Negligible performance impact',
}
