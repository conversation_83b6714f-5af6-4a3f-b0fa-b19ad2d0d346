"""
SHAP-Based Optimal Features - Generated 20250721_043719
Best Algorithm: Random Forest (Accuracy: 0.9492)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "consistency_score",  # SHAP: 0.2629
    "pomokit_title_count",  # SHAP: 0.0944
    "gamification_balance",  # SHAP: 0.0228
    "total_distance_km",  # SHAP: 0.0168
    "title_balance_ratio",  # SHAP: 0.0156
    "activity_days",  # SHAP: 0.0147
    "pomokit_title_length",  # SHAP: 0.0125
    "total_time_minutes",  # SHAP: 0.0112
    "strava_title_length",  # SHAP: 0.0083
    "strava_unique_words",  # SHAP: 0.0079
    "avg_time_minutes",  # SHAP: 0.0056
    "avg_distance_km",  # SHAP: 0.0031
    "pomokit_unique_words",  # SHAP: 0.0014
    "achievement_rate",  # SHAP: 0.0012
    "activity_points",  # SHAP: 0.0010
    "strava_title_count",  # SHAP: 0.0001
    "total_cycles",  # SHAP: 0.0000
    "total_title_diversity",  # SHAP: 0.0000
    "productivity_points",  # SHAP: 0.0000
    "work_days",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2629
    "pomokit_title_count",  # SHAP: 0.0944
    "gamification_balance",  # SHAP: 0.0228
    "total_distance_km",  # SHAP: 0.0168
    "title_balance_ratio",  # SHAP: 0.0156
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2629
    "pomokit_title_count",  # SHAP: 0.0944
    "gamification_balance",  # SHAP: 0.0228
    "total_distance_km",  # SHAP: 0.0168
    "title_balance_ratio",  # SHAP: 0.0156
    "activity_days",  # SHAP: 0.0147
    "pomokit_title_length",  # SHAP: 0.0125
    "total_time_minutes",  # SHAP: 0.0112
    "strava_title_length",  # SHAP: 0.0083
    "strava_unique_words",  # SHAP: 0.0079
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2629
    "pomokit_title_count",  # SHAP: 0.0944
    "gamification_balance",  # SHAP: 0.0228
    "total_distance_km",  # SHAP: 0.0168
    "title_balance_ratio",  # SHAP: 0.0156
    "activity_days",  # SHAP: 0.0147
    "pomokit_title_length",  # SHAP: 0.0125
    "total_time_minutes",  # SHAP: 0.0112
    "strava_title_length",  # SHAP: 0.0083
    "strava_unique_words",  # SHAP: 0.0079
    "avg_time_minutes",  # SHAP: 0.0056
    "avg_distance_km",  # SHAP: 0.0031
    "pomokit_unique_words",  # SHAP: 0.0014
    "achievement_rate",  # SHAP: 0.0012
    "activity_points",  # SHAP: 0.0010
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "consistency_score": 0.262864,
    "pomokit_title_count": 0.094430,
    "gamification_balance": 0.022798,
    "total_distance_km": 0.016831,
    "title_balance_ratio": 0.015613,
    "activity_days": 0.014658,
    "pomokit_title_length": 0.012462,
    "total_time_minutes": 0.011217,
    "strava_title_length": 0.008284,
    "strava_unique_words": 0.007883,
    "avg_time_minutes": 0.005624,
    "avg_distance_km": 0.003104,
    "pomokit_unique_words": 0.001355,
    "achievement_rate": 0.001226,
    "activity_points": 0.000971,
    "strava_title_count": 0.000051,
    "total_cycles": 0.000043,
    "total_title_diversity": 0.000010,
    "productivity_points": 0.000010,
    "work_days": 0.000000,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)