================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 291

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)
   5. XGBoost (xgboost)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [5, 10, 15, 20, 25, 30]
   • Total Experiments: 20

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: XGBoost
   • Features Used: 5
   • Accuracy: 0.9554 ± 0.0318
   • F1-Score: 0.9464
   • ✅ Overfitting Risk: 0.0051 (LOW - Train-Test gap < 5%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 15
     - Accuracy: 0.6224 ± 0.0821
     - F1-Score: 0.6250
     - ⚠️ Overfitting: 0.1001 (HIGH)
   • Random Forest:
     - Features: 10
     - Accuracy: 0.9520 ± 0.0367
     - F1-Score: 0.9416
     - ✅ Overfitting: 0.0446 (LOW)
   • Gradient Boosting:
     - Features: 5
     - Accuracy: 0.9486 ± 0.0324
     - F1-Score: 0.9365
     - ✅ Overfitting: 0.0308 (LOW)
   • Support Vector Machine:
     - Features: 15
     - Accuracy: 0.6565 ± 0.0392
     - F1-Score: 0.6562
     - ✅ Overfitting: 0.0497 (LOW)
   • XGBoost:
     - Features: 5
     - Accuracy: 0.9554 ± 0.0318
     - F1-Score: 0.9464
     - ✅ Overfitting: 0.0051 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. total_cycles: 20 times (100.0%)
    2. work_days: 20 times (100.0%)
    3. consistency_score: 20 times (100.0%)
    4. productivity_points: 18 times (90.0%)
    5. pomokit_title_count: 16 times (80.0%)
    6. strava_title_length: 16 times (80.0%)
    7. title_balance_ratio: 15 times (75.0%)
    8. gamification_balance: 13 times (65.0%)
    9. pomokit_title_length: 13 times (65.0%)
   10. total_distance_km: 13 times (65.0%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. total_cycles: 10/10 top results
    2. work_days: 10/10 top results
    3. productivity_points: 10/10 top results
    4. consistency_score: 10/10 top results
    5. strava_title_length: 8/10 top results
    6. strava_title_count: 7/10 top results
    7. total_distance_km: 7/10 top results
    8. pomokit_title_count: 6/10 top results
    9. gamification_balance: 6/10 top results
   10. title_balance_ratio: 6/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.0051
   • ✅ LOW OVERFITTING RISK: Good generalization expected

   📊 Overfitting by Algorithm:
     ⚠️ Logistic Regression: 0.1001 (HIGH)
     ✅ Random Forest: 0.0446 (LOW)
     ✅ Gradient Boosting: 0.0308 (LOW)
     ✅ Support Vector Machine: 0.0497 (LOW)
     ✅ XGBoost: 0.0051 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: XGBoost
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 5
   • Baseline Accuracy: 0.9554

   🎯 Feature Impact Analysis (Top 10):
      1. ⚪ productivity_points:
         - Baseline: 0.9554 | Without: 0.9520
         - Impact: 0.0034 (0.36%)
         - MINIMAL - Negligible performance impact
      2. ⚪ consistency_score:
         - Baseline: 0.9554 | Without: 0.9520
         - Impact: 0.0034 (0.36%)
         - MINIMAL - Negligible performance impact
      3. ⚪ strava_title_count:
         - Baseline: 0.9554 | Without: 0.9588
         - Impact: -0.0034 (-0.35%)
         - MINIMAL - Negligible performance impact
      4. ⚪ total_cycles:
         - Baseline: 0.9554 | Without: 0.9554
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
      5. ⚪ work_days:
         - Baseline: 0.9554 | Without: 0.9554
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact

   📊 Permutation Importance Summary:
     • productivity_points: 0.00% (score: 0.0000 ±0.0000)
     • consistency_score: 66.77% (score: 0.3818 ±0.0229)
     • strava_title_count: 3.85% (score: 0.0220 ±0.0087)
     • total_cycles: 28.91% (score: 0.1653 ±0.0137)
     • work_days: 0.48% (score: 0.0027 ±0.0034)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: XGBoost
   • Number of Features: 5
   • Selected Features:
      1. consistency_score
      2. productivity_points
      3. strava_title_count
      4. total_cycles
      5. work_days
================================================================================