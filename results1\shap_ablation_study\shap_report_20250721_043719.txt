================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 145, 'high_risk': 100, 'low_risk': 46}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.6441
     - Test F1-Score: 0.6412
     - CV Accuracy: 0.6764 (±0.0526)
     - CV F1-Score: 0.6587 (±0.0560)
   • Random Forest:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9568 (±0.0236)
     - CV F1-Score: 0.9572 (±0.0235)
   • Gradient Boosting:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9568 (±0.0236)
     - CV F1-Score: 0.9574 (±0.0234)
   • XGBoost:
     - Test Accuracy: 0.9322
     - Test F1-Score: 0.9333
     - CV Accuracy: 0.9352 (±0.0365)
     - CV F1-Score: 0.9348 (±0.0370)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. 🔥 consistency_score: 0.2630
      2. ⭐ pomokit_title_count: 0.0942
      3. 🔸 gamification_balance: 0.0230
      4. ▫️ total_distance_km: 0.0188
      5. ▫️ activity_days: 0.0158
      6. ▫️ title_balance_ratio: 0.0145
      7. ▫️ pomokit_title_length: 0.0121
      8. ▫️ total_time_minutes: 0.0093
      9. ▫️ strava_title_length: 0.0080
     10. ▫️ strava_unique_words: 0.0076

   📊 Random Forest:
      1. 🔥 consistency_score: 0.2629
      2. ⭐ pomokit_title_count: 0.0944
      3. 🔸 gamification_balance: 0.0228
      4. ▫️ total_distance_km: 0.0168
      5. ▫️ title_balance_ratio: 0.0156
      6. ▫️ activity_days: 0.0147
      7. ▫️ pomokit_title_length: 0.0125
      8. ▫️ total_time_minutes: 0.0112
      9. ▫️ strava_title_length: 0.0083
     10. ▫️ strava_unique_words: 0.0079

   📊 Gradient Boosting:
      1. 🔥 consistency_score: 0.2628
      2. ⭐ pomokit_title_count: 0.0941
      3. 🔸 gamification_balance: 0.0221
      4. ▫️ total_distance_km: 0.0179
      5. ▫️ title_balance_ratio: 0.0158
      6. ▫️ activity_days: 0.0140
      7. ▫️ pomokit_title_length: 0.0121
      8. ▫️ total_time_minutes: 0.0121
      9. ▫️ strava_unique_words: 0.0084
     10. ▫️ strava_title_length: 0.0083

   📊 XGBoost:
      1. 🔥 consistency_score: 0.2634
      2. ⭐ pomokit_title_count: 0.0940
      3. 🔸 gamification_balance: 0.0224
      4. ▫️ total_distance_km: 0.0176
      5. ▫️ title_balance_ratio: 0.0157
      6. ▫️ activity_days: 0.0141
      7. ▫️ total_time_minutes: 0.0122
      8. ▫️ pomokit_title_length: 0.0122
      9. ▫️ strava_title_length: 0.0087
     10. ▫️ strava_unique_words: 0.0084

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • consistency_score: 4/4 algorithms (100.0%)
     • pomokit_title_count: 4/4 algorithms (100.0%)
     • gamification_balance: 4/4 algorithms (100.0%)
     • total_distance_km: 4/4 algorithms (100.0%)
     • title_balance_ratio: 3/4 algorithms (75.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: Random Forest (0.9492 accuracy)
   • Top 5 SHAP features for production:
     1. consistency_score (SHAP: 0.2629)
     2. pomokit_title_count (SHAP: 0.0944)
     3. gamification_balance (SHAP: 0.0228)
     4. total_distance_km (SHAP: 0.0168)
     5. title_balance_ratio (SHAP: 0.0156)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 20
   • Best accuracy achieved: 0.9492
   • Analysis timestamp: 2025-07-21T04:37:19.017282